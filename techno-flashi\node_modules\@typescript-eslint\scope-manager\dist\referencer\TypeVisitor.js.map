{"version": 3, "file": "TypeVisitor.js", "sourceRoot": "", "sources": ["../../src/referencer/TypeVisitor.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAoE;AACpE,oCAAiD;AAEjD,uCAAoC;AAEpC,MAAM,WAAY,SAAQ,iBAAO;IAG/B,YAAY,UAAsB;QAChC,KAAK,CAAC,UAAU,CAAC,CAAC;QAHX,0CAAwB;QAI/B,uBAAA,IAAI,2BAAe,UAAU,MAAA,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAsB,EAAE,IAAmB;QACtD,MAAM,cAAc,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAC;QACnD,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,mBAAmB;IACnB,mBAAmB;IACnB,mBAAmB;IAET,iBAAiB,CACzB,IAK8B;QAE9B,gFAAgF;QAChF,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC1D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEhC,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,kBAAkB,GAAG,KAAK,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBACzC,8FAA8F;gBAC9F,uBAAA,IAAI,+BAAY;qBACb,YAAY,EAAE;qBACd,gBAAgB,CACf,OAAO,EACP,IAAI,gCAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAClD,CAAC;gBAEJ,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;oBAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;oBACnC,kBAAkB,GAAG,IAAI,CAAC;gBAC5B,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,qGAAqG;YACrG,uEAAuE;YACvE,IAAI,CAAC,kBAAkB,IAAI,gBAAgB,IAAI,KAAK,EAAE,CAAC;gBACrD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5B,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,gBAAgB,CACxB,IAA+D;QAE/D,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;QACT,CAAC;QACD,4FAA4F;QAC5F,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnC,CAAC;IAED,qBAAqB;IACrB,qBAAqB;IACrB,qBAAqB;IAEX,UAAU,CAAC,IAAyB;QAC5C,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IACtD,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACxB,2BAA2B;IAC7B,CAAC;IAES,0BAA0B,CAClC,IAAyC;QAEzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,wDAAwD;QACxD,kEAAkE;QAClE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAE7D,8FAA8F;QAC9F,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;QAExC,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAE7B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,+BAA+B,CACvC,IAA8C;QAE9C,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,cAAc,CAAC,IAA6B;QACpD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,YAAY,CAAC,IAA2B;QAChD,mGAAmG;QACnG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/B,mFAAmF;IACrF,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpC,IAAI,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAC7C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACnC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAES,WAAW,CAAC,IAA0B;QAC9C,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,KAAK,GAAG,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC;QAE5C;;;;UAIE;QACF,IACE,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,YAAY;YACrC,KAAK,CAAC,IAAI,KAAK,iBAAS,CAAC,UAAU,EACnC,CAAC;YACD,yEAAyE;YACzE,IAAI,YAAY,GAAG,KAAK,CAAC,KAA0B,CAAC;YACpD,OAAO,YAAY,EAAE,CAAC;gBACpB,IACE,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,YAAY;oBAC5C,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,UAAU,EAC1C,CAAC;oBACD,iCAAiC;oBACjC,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC;oBAClC,SAAS;gBACX,CAAC;gBACD,IAAI,YAAY,CAAC,IAAI,KAAK,iBAAS,CAAC,eAAe,EAAE,CAAC;oBACpD,KAAK,GAAG,YAAY,CAAC;oBACrB,MAAM;gBACR,CAAC;gBACD,MAAM;YACR,CAAC;QACH,CAAC;QAED,KAAK,CAAC,gBAAgB,CACpB,aAAa,CAAC,IAAI,EAClB,IAAI,2BAAc,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CACtD,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;IACvC,CAAC;IAES,sBAAsB,CAC9B,IAAqC;QAErC,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,wEAAwE;YACxE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEtB,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAES,YAAY,CAAC,IAA2B;QAChD,oEAAoE;QACpE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QACxD,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,iBAAiB,CAAC,IAAgC;QAC1D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAES,kBAAkB,CAAC,IAAiC;QAC5D,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC7B,sFAAsF;IACxF,CAAC;IAES,mBAAmB,CAAC,IAAkC;QAC9D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,8EAA8E;IAChF,CAAC;IAES,sBAAsB,CAC9B,IAAqC;QAErC,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAEhE,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,wEAAwE;YACxE,uBAAA,IAAI,+BAAY,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEhC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,uBAAA,IAAI,+BAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,uBAAA,IAAI,+BAAY;aACb,YAAY,EAAE;aACd,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,2BAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;QAEpE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC3B,CAAC;IAES,eAAe,CAAC,IAA8B;QACtD,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;YAC1D,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAED,qFAAqF;IAC3E,WAAW,CAAC,IAA0B;QAC9C,IAAI,UAGqB,CAAC;QAC1B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;YAC1D,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gBACzD,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACnB,CAAC;YACD,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC;YAE3B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;gBACvD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QACD,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;YAClD,uBAAA,IAAI,+BAAY,CAAC,YAAY,EAAE,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC7D,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACjC,CAAC;IAES,gBAAgB,CAAC,IAA+B;QACxD,QAAQ;QACR,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACF;AAEQ,kCAAW"}