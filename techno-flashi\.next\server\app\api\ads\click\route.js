(()=>{var e={};e.id=9565,e.ids=[9565],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4395:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>c});var i=t(96559),o=t(48088),n=t(37719),a=t(32190);let u=(0,t(66437).UU)(process.env.NEXT_PUBLIC_SUPABASE_URL,process.env.SUPABASE_SERVICE_ROLE_KEY);async function c(e){try{let{adId:r,position:t,timestamp:s}=await e.json();if(!r)return a.NextResponse.json({error:"Ad ID is required"},{status:400});let{error:i}=await u.from("ad_clicks").insert([{ad_id:r,position:t,clicked_at:s||new Date().toISOString(),ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||"unknown",user_agent:e.headers.get("user-agent")||"unknown"}]);return i&&console.error("Error logging ad click:",i),a.NextResponse.json({success:!0})}catch(e){return console.error("Error in ad click API:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/ads/click/route",pathname:"/api/ads/click",filename:"route",bundlePath:"app/api/ads/click/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\api\\ads\\click\\route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function g(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6437,580],()=>t(4395));module.exports=s})();