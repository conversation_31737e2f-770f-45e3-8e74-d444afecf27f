/* تأثيرات تفاعلية خفيفة */

/* تأثيرات الهيدر */
.header-interactive {
  position: relative;
  transition: all 0.3s ease-out;
}

.header-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(168, 85, 247, 0.05) 0%, rgba(236, 72, 153, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease-out;
  pointer-events: none;
}

.header-interactive:hover::before {
  opacity: 1;
}

/* تأثيرات الروابط في الهيدر */
.header-link {
  position: relative;
  transition: all 0.2s ease-out;
  overflow: hidden;
}

.header-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #a855f7, #ec4899);
  transition: width 0.3s ease-out;
}

.header-link:hover::after {
  width: 100%;
}

.header-link:hover {
  color: #a855f7;
  transform: translateY(-1px);
}

/* تأثيرات الهيرو سكشن */
.hero-interactive {
  position: relative;
  overflow: hidden;
}

.hero-interactive::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(168, 85, 247, 0.03) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.5s ease-out;
  pointer-events: none;
  transform: scale(0);
}

.hero-interactive:hover::before {
  opacity: 1;
  transform: scale(1);
}

/* تأثيرات الأزرار المحسنة */
.interactive-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.interactive-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease-out;
}

.interactive-button:hover::before {
  left: 100%;
}

.interactive-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 25px rgba(168, 85, 247, 0.3);
}

.interactive-button:active {
  transform: translateY(0) scale(0.98);
}

/* تأثيرات النص المتوهج */
.glow-text {
  position: relative;
  transition: all 0.3s ease-out;
}

.glow-text:hover {
  text-shadow: 
    0 0 5px rgba(168, 85, 247, 0.3),
    0 0 10px rgba(168, 85, 247, 0.2),
    0 0 15px rgba(168, 85, 247, 0.1);
}

/* تأثيرات الإحصائيات */
.stat-item {
  transition: all 0.3s ease-out;
  cursor: default;
}

.stat-item:hover {
  transform: translateY(-5px) scale(1.05);
}

.stat-item:hover .stat-number {
  background: linear-gradient(135deg, #a855f7, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* تأثيرات الجسيمات المتحركة */
@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(120deg); }
  66% { transform: translateY(5px) rotate(240deg); }
}

.floating-particle {
  animation: float 6s ease-in-out infinite;
}

.floating-particle:nth-child(2) {
  animation-delay: -2s;
}

.floating-particle:nth-child(3) {
  animation-delay: -4s;
}

/* تأثيرات التمرير السلس */
.smooth-scroll {
  scroll-behavior: smooth;
}

/* تأثيرات الظلال التفاعلية */
.interactive-shadow {
  transition: box-shadow 0.3s ease-out;
}

.interactive-shadow:hover {
  box-shadow: 
    0 4px 15px rgba(168, 85, 247, 0.1),
    0 8px 25px rgba(236, 72, 153, 0.1);
}

/* تأثيرات الخلفية المتدرجة */
.gradient-bg-interactive {
  background: linear-gradient(135deg, #ffffff 0%, #faf5ff 50%, #fdf2f8 100%);
  transition: background 0.5s ease-out;
}

.gradient-bg-interactive:hover {
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 50%, #fce7f3 100%);
}

/* تحسينات الأداء */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}

/* تأثيرات الاستجابة للمس */
@media (hover: none) and (pointer: coarse) {
  .interactive-button:hover {
    transform: none;
    box-shadow: none;
  }
  
  .header-link:hover {
    transform: none;
  }
  
  .stat-item:hover {
    transform: none;
  }
}

/* تقليل الحركة للمستخدمين الذين يفضلون ذلك */
@media (prefers-reduced-motion: reduce) {
  .interactive-button,
  .header-link,
  .stat-item,
  .glow-text,
  .floating-particle {
    transition: none;
    animation: none;
  }
}
