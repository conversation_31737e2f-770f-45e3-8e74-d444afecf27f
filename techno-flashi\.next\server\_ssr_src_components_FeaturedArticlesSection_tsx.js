"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_FeaturedArticlesSection_tsx";
exports.ids = ["_ssr_src_components_FeaturedArticlesSection_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/ArticleCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ArticleCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ArticleCard: () => (/* binding */ ArticleCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n// هذا مكون واجهة مستخدم قابل لإعادة الاستخدام لعرض \"بطاقة مقال\"\n/* __next_internal_client_entry_do_not_use__ ArticleCard auto */ \n\n\nfunction ArticleCard({ article }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n        href: `/articles/${article.slug}`,\n        className: \"block group focus-modern rounded-2xl\",\n        \"aria-label\": `قراءة مقال: ${article.title}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"modern-card overflow-hidden hover-lift smooth-transition h-full border border-gray-200 hover:border-blue-300\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-48 overflow-hidden bg-gradient-to-br from-purple-100 to-blue-100 rounded-t-2xl\",\n                    children: [\n                        article.featured_image_url && !article.featured_image_url.includes('placehold.co') ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    src: article.featured_image_url,\n                                    alt: `صورة مقال: ${article.title}`,\n                                    width: 600,\n                                    height: 400,\n                                    style: {\n                                        objectFit: \"cover\",\n                                        width: \"100%\",\n                                        height: \"100%\"\n                                    },\n                                    className: \"transition-transform duration-500 group-hover:scale-110\",\n                                    priority: false,\n                                    quality: 80,\n                                    sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                                    onError: (e)=>{\n                                        // إخفاء الصورة في حالة الخطأ وإظهار الأيقونة\n                                        e.currentTarget.style.display = 'none';\n                                        const fallback = e.currentTarget.parentElement?.querySelector('.fallback-icon');\n                                        if (fallback) {\n                                            fallback.style.display = 'flex';\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true) : null,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `fallback-icon absolute inset-0 flex items-center justify-center ${article.featured_image_url && !article.featured_image_url.includes('placehold.co') ? 'hidden' : 'flex'}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-8 h-8 text-purple-600\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-purple-700 font-medium\",\n                                        children: \"TechnoFlash\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2\",\n                            children: article.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-sm mb-6 leading-relaxed line-clamp-3\",\n                            children: article.excerpt\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-600 font-medium\",\n                                    children: new Date(article.published_at).toLocaleDateString('ar-EG', {\n                                        year: 'numeric',\n                                        month: 'long',\n                                        day: 'numeric'\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:translate-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"اقرأ المزيد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 transform group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ArticleCard.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ArticleCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FeaturedArticlesSection.tsx":
/*!****************************************************!*\
  !*** ./src/components/FeaturedArticlesSection.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedArticlesSection: () => (/* binding */ FeaturedArticlesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _ArticleCard__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ArticleCard */ \"(ssr)/./src/components/ArticleCard.tsx\");\n// قسم المقالات المميزة (3 مقالات جنباً إلى جنب)\n\n\nfunction FeaturedArticlesSection({ articles }) {\n    if (articles.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-20 px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-12 h-12 text-gray-500\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold text-gray-900 mb-2\",\n                            children: \"لا توجد مقالات بعد\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-text-description\",\n                            children: \"سنقوم بنشر المقالات قريباً، ترقبوا المحتوى الجديد!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this);\n    }\n    // أول 3 مقالات للعرض الجانبي\n    const latestArticles = articles.slice(0, 3);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"heading-1 mb-4\",\n                                    children: \"أحدث المقالات\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-description\",\n                                    children: \"اكتشف آخر ما نشرناه من محتوى تقني مفيد\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: \"/articles\",\n                            className: \"text-primary hover:text-blue-400 font-semibold transition-colors duration-300 flex items-center\",\n                            children: [\n                                \"عرض الكل\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-5 h-5 mr-2\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M15 19l-7-7 7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: latestArticles.length > 0 ? latestArticles.map((article)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ArticleCard__WEBPACK_IMPORTED_MODULE_1__.ArticleCard, {\n                            article: article\n                        }, article.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 15\n                        }, this)) : // إذا لم تكن هناك مقالات، نعرض رسالة\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-full text-center py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"المزيد من المقالات قريباً...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\FeaturedArticlesSection.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FeaturedArticlesSection.tsx\n");

/***/ })

};
;