"use strict";(()=>{var e={};e.id=8758,e.ids=[8758],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},14461:(e,s,r)=>{r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),l=r(88170),i=r.n(l),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["setup-admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34064)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\setup-admin\\page.tsx"]}]},{metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\setup-admin\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/setup-admin/page",pathname:"/setup-admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34064:(e,s,r)=>{r.r(s),r.d(s,{default:()=>a});var t=r(37413);function a(){return(0,t.jsx)("div",{className:"min-h-screen py-20 px-4",children:(0,t.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-white mb-4",children:"إعداد مستخدم المدير"}),(0,t.jsx)("p",{className:"text-xl text-dark-text-secondary",children:"اتبع هذه الخطوات لإنشاء مستخدم مدير في Supabase"})]}),(0,t.jsxs)("div",{className:"bg-dark-card rounded-xl p-8 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"خطوات الإعداد:"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4",children:"1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"افتح Supabase Dashboard"}),(0,t.jsx)("p",{className:"text-dark-text-secondary mb-2",children:"اذهب إلى رابط مشروعك في Supabase:"}),(0,t.jsx)("a",{href:"https://supabase.com/dashboard/project/zgktrwpladrkhhemhnni",target:"_blank",rel:"noopener noreferrer",className:"inline-block bg-primary text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-300",children:"فتح Supabase Dashboard"})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4",children:"2"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"انتقل إلى قسم Authentication"}),(0,t.jsxs)("p",{className:"text-dark-text-secondary",children:["في الشريط الجانبي، اضغط على ",(0,t.jsx)("strong",{className:"text-white",children:"Authentication"})," ثم ",(0,t.jsx)("strong",{className:"text-white",children:"Users"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4",children:"3"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"أنشئ مستخدم جديد"}),(0,t.jsxs)("p",{className:"text-dark-text-secondary mb-3",children:["اضغط على زر ",(0,t.jsx)("strong",{className:"text-white",children:'"Add user"'})," وأدخل البيانات التالية:"]}),(0,t.jsxs)("div",{className:"bg-dark-background p-4 rounded-lg border border-gray-700",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email:"}),(0,t.jsx)("code",{className:"block bg-gray-800 text-green-400 p-2 rounded text-sm",children:"<EMAIL>"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Password:"}),(0,t.jsx)("code",{className:"block bg-gray-800 text-green-400 p-2 rounded text-sm",children:"Admin123456!"})]})]}),(0,t.jsx)("div",{className:"mt-3",children:(0,t.jsxs)("label",{className:"flex items-center text-sm text-gray-300",children:[(0,t.jsx)("input",{type:"checkbox",checked:!0,disabled:!0,className:"mr-2"}),"Auto Confirm User (مفعل)"]})})]})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white font-bold mr-4",children:"4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"احفظ المستخدم"}),(0,t.jsxs)("p",{className:"text-dark-text-secondary",children:["اضغط على ",(0,t.jsx)("strong",{className:"text-white",children:'"Create user"'})," لحفظ المستخدم"]})]})]}),(0,t.jsxs)("div",{className:"flex items-start",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold mr-4",children:"✓"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-2",children:"اختبر تسجيل الدخول"}),(0,t.jsx)("p",{className:"text-dark-text-secondary mb-3",children:"الآن يمكنك تسجيل الدخول باستخدام البيانات المنشأة:"}),(0,t.jsx)("a",{href:"/login",className:"inline-block bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors duration-300",children:"اذهب لصفحة تسجيل الدخول"})]})]})]})]}),(0,t.jsxs)("div",{className:"mt-8 bg-yellow-900/20 border border-yellow-500/30 rounded-xl p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-yellow-400 mb-3",children:"⚠️ ملاحظات مهمة:"}),(0,t.jsxs)("ul",{className:"space-y-2 text-dark-text-secondary",children:[(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-400 mr-2",children:"•"}),'تأكد من تفعيل "Auto Confirm User" لتجنب الحاجة لتأكيد البريد الإلكتروني']}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-400 mr-2",children:"•"}),"استخدم كلمة مرور قوية في البيئة الإنتاجية"]}),(0,t.jsxs)("li",{className:"flex items-start",children:[(0,t.jsx)("span",{className:"text-yellow-400 mr-2",children:"•"}),"يمكنك تغيير البريد الإلكتروني وكلمة المرور لاحقاً من Supabase Dashboard"]})]})]}),(0,t.jsxs)("div",{className:"mt-8 text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"روابط مفيدة:"}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,t.jsx)("a",{href:"https://supabase.com/dashboard/project/zgktrwpladrkhhemhnni/auth/users",target:"_blank",rel:"noopener noreferrer",className:"bg-primary text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors duration-300",children:"إدارة المستخدمين في Supabase"}),(0,t.jsx)("a",{href:"/login",className:"border border-primary text-primary hover:bg-primary hover:text-white px-6 py-3 rounded-lg transition-all duration-300",children:"صفحة تسجيل الدخول"})]})]})]})})}},34631:e=>{e.exports=require("tls")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,6437,2675,3595],()=>r(14461));module.exports=t})();