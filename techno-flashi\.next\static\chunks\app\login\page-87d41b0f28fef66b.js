(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{9690:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var a=t(95155),r=t(12115),l=t(40283);function n(){let[e,s]=(0,r.useState)(""),[t,n]=(0,r.useState)(""),[d,i]=(0,r.useState)(null),[o,c]=(0,r.useState)(!1),{signIn:u}=(0,l.A)(),m=async s=>{if(s.preventDefault(),s.stopPropagation(),!e||!t)return void i("يرجى إدخال البريد الإلكتروني وكلمة المرور");c(!0),i(null);try{await u(e,t)}catch(s){console.error("❌ خطأ في تسجيل الدخول:",s);let e="حدث خطأ غير متوقع";i("Invalid login credentials"===s.message?"البريد الإلكتروني أو كلمة المرور غير صحيحة.":s.message.includes("Email not confirmed")?"يرجى تأكيد البريد الإلكتروني أولاً.":s.message.includes("Too many requests")?"محاولات كثيرة جداً. يرجى المحاولة لاحقاً.":s.message||"حدث خطأ في الاتصال")}finally{c(!1)}};return(0,a.jsx)("div",{className:"flex justify-center items-center mt-16",children:(0,a.jsxs)("div",{className:"w-full max-w-md p-8 space-y-6 bg-dark-card rounded-lg shadow-lg",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-center text-white",children:"تسجيل الدخول"}),(0,a.jsxs)("form",{onSubmit:m,className:"space-y-6",method:"post",action:"#",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"البريد الإلكتروني"}),(0,a.jsx)("input",{type:"email",value:e,onChange:e=>s(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"كلمة المرور"}),(0,a.jsx)("input",{type:"password",value:t,onChange:e=>n(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),d&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:d}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"w-full px-4 py-2 font-bold text-white bg-primary rounded-md hover:bg-primary/90 disabled:bg-gray-500",children:o?"جاري الدخول...":"دخول"})]})]})})}},65654:(e,s,t)=>{Promise.resolve().then(t.bind(t,9690))}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>s(65654)),_N_E=e.O()}]);