(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6693],{85799:(e,s,l)=>{Promise.resolve().then(l.bind(l,95579))},95579:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>c});var a=l(95155),r=l(12115),t=l(42099),d=l(6874),i=l.n(d);function c(){let[e,s]=(0,r.useState)(!1),[l,d]=(0,r.useState)(null),c=async()=>{s(!0);try{let{data:e,error:s}=await t.ND.from("ads").select("id, title, status, created_at").limit(10),{data:l,error:a}=await t.ND.from("advertisements").select("id, title, is_active, created_at").limit(10);d({success:!0,oldTable:{exists:!s,count:(null==e?void 0:e.length)||0,sample:e||[]},newTable:{exists:!a,count:(null==l?void 0:l.length)||0,sample:l||[]}})}catch(e){d({success:!1,error:e.message})}finally{s(!1)}},n=async()=>{if(confirm("هل أنت متأكد من ترحيل البيانات من الجدول القديم؟")){s(!0);try{let{error:e}=await t.ND.rpc("migrate_ads_to_advertisements");if(e){let{error:e}=await t.ND.from("advertisements").insert([])}alert("تم ترحيل البيانات بنجاح"),await c()}catch(e){alert("خطأ في الترحيل: "+e.message)}finally{s(!1)}}},x=async()=>{if(confirm("⚠️ تحذير: هل أنت متأكد من حذف الجدول القديم؟ هذا الإجراء لا يمكن التراجع عنه!")&&confirm("تأكيد نهائي: سيتم حذف جدول ads القديم نهائياً!")){s(!0);try{await t.ND.rpc("drop_old_ads_table"),alert("تم حذف الجدول القديم بنجاح"),await c()}catch(e){alert("خطأ في حذف الجدول: "+e.message)}finally{s(!1)}}};return(0,a.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"ترحيل بيانات الإعلانات"}),(0,a.jsx)("p",{className:"text-dark-text-secondary",children:"ترحيل البيانات من الجدول القديم (ads) إلى الجدول الجديد (advertisements)"})]}),(0,a.jsx)(i(),{href:"/admin/ads",className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors",children:"العودة للإعلانات"})]}),(0,a.jsxs)("div",{className:"bg-yellow-900 border border-yellow-700 rounded-xl p-6 mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-yellow-300 mb-4",children:"\uD83D\uDD0D المشكلة المكتشفة"}),(0,a.jsxs)("div",{className:"text-yellow-100 space-y-2",children:[(0,a.jsxs)("p",{children:["• لديك جدولين منفصلين للإعلانات: ",(0,a.jsx)("code",{children:"ads"})," (القديم) و ",(0,a.jsx)("code",{children:"advertisements"})," (الجديد)"]}),(0,a.jsx)("p",{children:"• لوحة التحكم تقرأ من الجدول الجديد بينما بعض المكونات تقرأ من القديم"}),(0,a.jsx)("p",{children:"• هذا يسبب عدم تطابق في البيانات المعروضة"}),(0,a.jsx)("p",{children:"• الرابط الذي أرسلته يشير إلى محرر SQL في Supabase حيث يمكنك رؤية هذه المشكلة"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"فحص الجداول"}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"فحص حالة الجدولين القديم والجديد"}),(0,a.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:e?"جاري الفحص...":"فحص الجداول"})]}),(0,a.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"ترحيل البيانات"}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"ترحيل البيانات المتبقية من القديم للجديد"}),(0,a.jsx)("button",{onClick:n,disabled:e,className:"w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:e?"جاري الترحيل...":"ترحيل البيانات"})]}),(0,a.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"حذف الجدول القديم"}),(0,a.jsx)("p",{className:"text-gray-300 mb-4",children:"حذف الجدول القديم نهائياً (خطر!)"}),(0,a.jsx)("button",{onClick:x,disabled:e,className:"w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:e?"جاري الحذف...":"⚠️ حذف الجدول القديم"})]})]}),l&&(0,a.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:l.success?"✅ نتائج الفحص":"❌ خطأ في الفحص"}),l.success?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-3",children:"\uD83D\uDCCA الجدول القديم (ads)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,a.jsx)("span",{className:l.oldTable.exists?"text-green-400":"text-red-400",children:l.oldTable.exists?"موجود":"غير موجود"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,a.jsx)("span",{className:"text-white",children:l.oldTable.count})]})]}),l.oldTable.sample.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.oldTable.sample.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.status]},e.id))})]})]}),(0,a.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-3",children:"\uD83D\uDCCA الجدول الجديد (advertisements)"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,a.jsx)("span",{className:l.newTable.exists?"text-green-400":"text-red-400",children:l.newTable.exists?"موجود":"غير موجود"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,a.jsx)("span",{className:"text-white",children:l.newTable.count})]})]}),l.newTable.sample.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,a.jsx)("div",{className:"space-y-1",children:l.newTable.sample.slice(0,3).map(e=>(0,a.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.is_active?"نشط":"غير نشط"]},e.id))})]})]})]}),(0,a.jsxs)("div",{className:"bg-blue-900 border border-blue-700 rounded-lg p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-300 mb-3",children:"\uD83D\uDCA1 التوصيات"}),(0,a.jsxs)("div",{className:"text-blue-100 space-y-2",children:[l.oldTable.exists&&l.oldTable.count>0&&(0,a.jsx)("p",{children:"• يُنصح بترحيل البيانات المتبقية من الجدول القديم"}),l.oldTable.exists&&0===l.oldTable.count&&(0,a.jsx)("p",{children:"• يمكن حذف الجدول القديم بأمان لأنه فارغ"}),(0,a.jsx)("p",{children:"• تأكد من أن جميع المكونات تستخدم الجدول الجديد (advertisements)"}),(0,a.jsx)("p",{children:"• قم بعمل نسخة احتياطية قبل حذف الجدول القديم"})]})]})]}):(0,a.jsx)("div",{className:"bg-red-900 border border-red-700 rounded-lg p-4",children:(0,a.jsx)("p",{className:"text-red-300",children:l.error})})]}),(0,a.jsxs)("div",{className:"mt-8 bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"معلومات الترحيل"}),(0,a.jsxs)("div",{className:"space-y-3 text-gray-300",children:[(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"الجدول القديم (ads):"})," يستخدم status, placement, description"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"الجدول الجديد (advertisements):"})," يستخدم is_active, position, content"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"الترحيل:"})," يحول البيانات من الهيكل القديم للجديد تلقائياً"]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"الحذف:"})," يحذف الجدول القديم نهائياً بعد التأكد من الترحيل"]})]})]}),(0,a.jsx)("div",{className:"mt-8 text-center",children:(0,a.jsxs)("div",{className:"space-x-4 space-x-reverse",children:[(0,a.jsx)(i(),{href:"/admin/ads",className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"إدارة الإعلانات"}),(0,a.jsx)(i(),{href:"/admin/ads/sync",className:"inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"مزامنة البيانات"}),(0,a.jsx)("a",{href:"https://supabase.com/dashboard/project/zgktrwpladrkhhemhnni/editor",target:"_blank",rel:"noopener noreferrer",className:"inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"محرر SQL"})]})})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>s(85799)),_N_E=e.O()}]);