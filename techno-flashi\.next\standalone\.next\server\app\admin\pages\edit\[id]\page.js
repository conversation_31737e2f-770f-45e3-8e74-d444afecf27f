(()=>{var e={};e.id=2540,e.ids=[2540],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>n});var s=t(60687),a=t(63213),i=t(16189);function n({children:e}){let{user:r,loading:t}=(0,a.A)();return((0,i.useRouter)(),t)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,s.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}t(43210)},27910:e=>{"use strict";e.exports=require("stream")},28279:(e,r,t)=>{Promise.resolve().then(t.bind(t,57468))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,r,t)=>{Promise.resolve().then(t.bind(t,67083))},55258:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\pages\\\\edit\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\pages\\edit\\[id]\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56089:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["pages",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,55258)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\pages\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\pages\\edit\\[id]\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/pages/edit/[id]/page",pathname:"/admin/pages/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},57468:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(16189),n=t(85814),o=t.n(n);function d({params:e}){let r=(0,i.useRouter)(),[t,n]=(0,a.useState)(!0),[d,l]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),[u,p]=(0,a.useState)(""),[x,h]=(0,a.useState)(""),[b,g]=(0,a.useState)({id:"",page_key:"",title_ar:"",content_ar:"",meta_description:"",meta_keywords:"",is_active:!0,display_order:0}),f=e=>{let{name:r,value:t,type:s}=e.target;g(a=>({...a,[r]:"checkbox"===s?e.target.checked:t}))},y=async e=>{if(e.preventDefault(),!b.title_ar.trim()||!b.content_ar.trim())return void m("العنوان والمحتوى مطلوبان");try{l(!0),m(""),p("");let e=await fetch(`/api/pages/${x}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({title_ar:b.title_ar,content_ar:b.content_ar,meta_description:b.meta_description,meta_keywords:b.meta_keywords,is_active:b.is_active,display_order:b.display_order})}),t=await e.json();t.success?(p("تم حفظ التغييرات بنجاح"),setTimeout(()=>{r.push("/admin/pages")},2e3)):m(t.message||"فشل في حفظ التغييرات")}catch(e){console.error("خطأ في حفظ الصفحة:",e),m("حدث خطأ أثناء حفظ التغييرات")}finally{l(!1)}};return t?(0,s.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})})}):(0,s.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"تحرير الصفحة"}),(0,s.jsxs)("p",{className:"text-dark-text-secondary",children:["تحرير محتوى صفحة: ",b.title_ar]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(o(),{href:`/page/${b.page_key}`,target:"_blank",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"معاينة الصفحة"}),(0,s.jsx)(o(),{href:"/admin/pages",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"العودة للقائمة"})]})]}),c&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-400",children:c})}),u&&(0,s.jsx)("div",{className:"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-green-400",children:u})}),(0,s.jsxs)("form",{onSubmit:y,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"المعلومات الأساسية"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"عنوان الصفحة *"}),(0,s.jsx)("input",{type:"text",name:"title_ar",value:b.title_ar,onChange:f,required:!0,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"مفتاح الصفحة"}),(0,s.jsx)("input",{type:"text",value:b.page_key,disabled:!0,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-gray-400 cursor-not-allowed"}),(0,s.jsx)("p",{className:"text-xs text-dark-text-secondary mt-1",children:"لا يمكن تعديل مفتاح الصفحة بعد الإنشاء"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"ترتيب العرض"}),(0,s.jsx)("input",{type:"number",name:"display_order",value:b.display_order,onChange:f,min:"0",className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"})]}),(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",name:"is_active",checked:b.is_active,onChange:f,className:"w-4 h-4 text-primary bg-dark-background border-gray-600 rounded focus:ring-primary focus:ring-2"}),(0,s.jsx)("span",{className:"mr-2 text-white",children:"صفحة نشطة"})]})})]})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"تحسين محركات البحث (SEO)"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"وصف الصفحة (Meta Description)"}),(0,s.jsx)("textarea",{name:"meta_description",value:b.meta_description||"",onChange:f,rows:3,maxLength:160,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"وصف مختصر للصفحة (160 حرف كحد أقصى)"}),(0,s.jsxs)("p",{className:"text-xs text-dark-text-secondary mt-1",children:[(b.meta_description||"").length,"/160 حرف"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"الكلمات المفتاحية (Meta Keywords)"}),(0,s.jsx)("input",{type:"text",name:"meta_keywords",value:b.meta_keywords||"",onChange:f,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"كلمة1, كلمة2, كلمة3"})]})]})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"محتوى الصفحة *"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("textarea",{name:"content_ar",value:b.content_ar,onChange:f,rows:20,required:!0,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary font-mono text-sm",placeholder:"أدخل محتوى الصفحة بصيغة HTML..."}),(0,s.jsxs)("div",{className:"text-xs text-dark-text-secondary",children:[(0,s.jsx)("p",{className:"mb-2",children:"يمكنك استخدام HTML لتنسيق المحتوى. أمثلة:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-400",children:[(0,s.jsx)("li",{children:"<h2>عنوان فرعي</h2>"}),(0,s.jsx)("li",{children:"<p>فقرة نصية</p>"}),(0,s.jsx)("li",{children:"<strong>نص عريض</strong>"}),(0,s.jsx)("li",{children:'<a href="رابط">نص الرابط</a>'})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(o(),{href:"/admin/pages",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"إلغاء"}),(0,s.jsx)("button",{type:"submit",disabled:d,className:"bg-primary hover:bg-blue-600 disabled:bg-gray-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-300 disabled:cursor-not-allowed",children:d?"جاري الحفظ...":"حفظ التغييرات"})]})]})]})})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91327:(e,r,t)=>{Promise.resolve().then(t.bind(t,55258))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97579:(e,r,t)=>{Promise.resolve().then(t.bind(t,20769))},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),a=t(67083);function i({children:e}){return(0,s.jsx)(a.ProtectedRoute,{children:(0,s.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}t(31240)}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6437,2675,3595],()=>t(56089));module.exports=s})();