"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1459],{5646:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let i=r(s(19936));t.PostgrestClient=i.default;let n=r(s(35068));t.PostgrestQueryBuilder=n.default;let a=r(s(83280));t.PostgrestFilterBuilder=a.default;let o=r(s(57156));t.PostgrestTransformBuilder=o.default;let h=r(s(9286));t.PostgrestBuilder=h.default;let l=r(s(41971));t.Postgrest<PERSON>rror=l.default,t.default={PostgrestClient:i.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:h.default,PostgrestError:l.default}},9286:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(92410)),n=r(s(41971));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=i.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,r;let i=null,a=null,o=null,h=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let r=null==(t=this.headers.Prefer)?void 0:t.match(/count=(exact|planned|estimated)/),n=null==(s=e.headers.get("content-range"))?void 0:s.split("/");r&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(i={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,h=406,l="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{i=JSON.parse(t),Array.isArray(i)&&404===e.status&&(a=[],i=null,h=200,l="OK")}catch(s){404===e.status&&""===t?(h=204,l="No Content"):i={message:t}}if(i&&this.isMaybeSingle&&(null==(r=null==i?void 0:i.details)?void 0:r.includes("0 rows"))&&(i=null,h=200,l="OK"),i&&this.shouldThrowOnError)throw new n.default(i)}return{error:i,data:a,count:o,status:h,statusText:l}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,r;return{error:{message:`${null!=(t=null==e?void 0:e.name)?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!=(s=null==e?void 0:e.stack)?s:""}`,hint:"",code:`${null!=(r=null==e?void 0:e.code)?r:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},10182:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},19936:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(35068)),n=r(s(83280)),a=s(85171);class o{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){let t=new URL(`${this.url}/${e}`);return new i.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:i}={}){let a,o,h=new URL(`${this.url}/rpc/${e}`);s||r?(a=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{h.searchParams.append(e,t)})):(a="POST",o=t);let l=Object.assign({},this.headers);return i&&(l.Prefer=`count=${i}`),new n.default({method:a,url:h,headers:l,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},35068:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(83280));class n{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){let r=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!r?"":('"'===e&&(r=!r),e)).join("");return this.url.searchParams.set("select",n),s&&(this.headers.Prefer=`count=${s}`),new i.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){let r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){let a=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),r&&a.push(`count=${r}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new i.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new i.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new i.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},41971:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class s extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=s},57156:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(9286));class n extends i.default{select(e){let t=!1,s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:i=r}={}){let n=i?`${i}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){let r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){let i=void 0===r?"offset":`${r}.offset`,n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:i=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,i?"wal":null].filter(Boolean).join("|"),h=null!=(a=this.headers.Accept)?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${h}"; options=${o};`,this}rollback(){var e;return(null!=(e=this.headers.Prefer)?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},76572:(e,t,s)=>{s.d(t,{UU:()=>ew});var r,i,n,a,o,h,l,c,u,d,f=s(80414);let{PostgrestClient:p,PostgrestQueryBuilder:m,PostgrestFilterBuilder:g,PostgrestTransformBuilder:v,PostgrestBuilder:b,PostgrestError:y}=s(5646);var _=s(9853);!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(r||(r={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(i||(i={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(n||(n={})),(a||(a={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(o||(o={}));class w{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){let r=t.getUint8(1),i=t.getUint8(2),n=this.HEADER_LENGTH+2,a=s.decode(e.slice(n,n+r));n+=r;let o=s.decode(e.slice(n,n+i));return n+=i,{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(n,e.byteLength)))}}}class k{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(h||(h={}));let j=(e,t,s={})=>{var r;let i=null!=(r=s.skipTypes)?r:[];return Object.keys(t).reduce((s,r)=>(s[r]=E(r,e,t,i),s),{})},E=(e,t,s,r)=>{let i=t.find(t=>t.name===e),n=null==i?void 0:i.type,a=s[e];return n&&!r.includes(n)?P(n,a):O(a)},P=(e,t)=>{if("_"===e.charAt(0))return R(t,e.slice(1,e.length));switch(e){case h.bool:return $(t);case h.float4:case h.float8:case h.int2:case h.int4:case h.int8:case h.numeric:case h.oid:return T(t);case h.json:case h.jsonb:return C(t);case h.timestamp:return S(t);case h.abstime:case h.date:case h.daterange:case h.int4range:case h.int8range:case h.money:case h.reltime:case h.text:case h.time:case h.timestamptz:case h.timetz:case h.tsrange:case h.tstzrange:default:return O(t)}},O=e=>e,$=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},T=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},C=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},R=(e,t)=>{if("string"!=typeof e)return e;let s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r,i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(e){r=i?i.split(","):[]}return r.map(e=>P(t,e))}return e},S=e=>"string"==typeof e?e.replace(" ","T"):e,A=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class U{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null==(s=this.receivedResp)?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(l||(l={}));class L{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=L.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=L.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],r()}),this.channel._on(s.diff,{},e=>{let{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=L.syncDiff(this.state,e,t,s),r())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){let i=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(i,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let s=i[e];if(s){let r=t.map(e=>e.presence_ref),i=s.map(e=>e.presence_ref),n=t.filter(e=>0>i.indexOf(e.presence_ref)),h=s.filter(e=>0>r.indexOf(e.presence_ref));n.length>0&&(a[e]=n),h.length>0&&(o[e]=h)}else a[e]=t}),this.syncDiff(i,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){let{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,(t,r)=>{var i;let n=null!=(i=e[t])?i:[];if(e[t]=this.cloneDeep(r),n.length>0){let s=e[t].map(e=>e.presence_ref),r=n.filter(e=>0>s.indexOf(e.presence_ref));e[t].unshift(...r)}s(t,n,r)}),this.map(n,(t,s)=>{let i=e[t];if(!i)return;let n=s.map(e=>e.presence_ref);i=i.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=i,r(t,i,s),0===i.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,s)=>{let r=e[s];return"metas"in r?t[s]=r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[s]=r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(c||(c={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(u||(u={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(d||(d={}));class x{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=i.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new U(this,n.join,this.params,this.timeout),this.rejoinTimer=new k(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=i.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=i.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=i.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=i.errored,this.rejoinTimer.scheduleTimeout())}),this._on(n.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new L(this),this.broadcastEndpointURL=A(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.state==i.closed){let{config:{broadcast:n,presence:a,private:o}}=this.params;this._onError(t=>null==e?void 0:e(d.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(d.CLOSED));let h={},l={broadcast:n,presence:a,postgres_changes:null!=(r=null==(s=this.bindings.postgres_changes)?void 0:s.map(e=>e.filter))?r:[],private:o};this.socket.accessTokenValue&&(h.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},h)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0===t){null==e||e(d.SUBSCRIBED);return}{let r=this.bindings.postgres_changes,n=null!=(s=null==r?void 0:r.length)?s:0,a=[];for(let s=0;s<n;s++){let n=r[s],{filter:{event:o,schema:h,table:l,filter:c}}=n,u=t&&t[s];if(u&&u.event===o&&u.schema===h&&u.table===l&&u.filter===c)a.push(Object.assign(Object.assign({},n),{id:u.id}));else{this.unsubscribe(),this.state=i.errored,null==e||e(d.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=a,e&&e(d.SUBSCRIBED);return}}).receive("error",t=>{this.state=i.errored,null==e||e(d.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(d.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,i,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null==(n=null==(i=null==(r=this.params)?void 0:r.config)?void 0:i.broadcast)?void 0:n.ack)||s("ok"),a.receive("ok",()=>s("ok")),a.receive("error",()=>s("error")),a.receive("timeout",()=>s("timed out"))});{let{event:i,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!=(s=t.timeout)?s:this.timeout);return await (null==(r=e.body)?void 0:r.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=i.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(n.close,"leave",this._joinRef())};this.joinPush.destroy();let s=null;return new Promise(r=>{(s=new U(this,n.leave,{},e)).receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})}).finally(()=>{null==s||s.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){let r=new AbortController,i=setTimeout(()=>r.abort(),s),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),n}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new U(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;let a=e.toLocaleLowerCase(),{close:o,error:h,leave:l,join:c}=n;if(s&&[o,h,l,c].indexOf(a)>=0&&s!==this._joinRef())return;let u=this._onMessage(a,t,s);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(a)?null==(r=this.bindings.postgres_changes)||r.filter(e=>{var t,s,r;return(null==(t=e.filter)?void 0:t.event)==="*"||(null==(r=null==(s=e.filter)?void 0:s.event)?void 0:r.toLocaleLowerCase())===a}).map(e=>e.callback(u,s)):null==(i=this.bindings[a])||i.filter(e=>{var s,r,i,n,o,h;if(!["broadcast","presence","postgres_changes"].includes(a))return e.type.toLocaleLowerCase()===a;if("id"in e){let n=e.id,a=null==(s=e.filter)?void 0:s.event;return n&&(null==(r=t.ids)?void 0:r.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null==(i=t.data)?void 0:i.type.toLocaleLowerCase()))}{let s=null==(o=null==(n=null==e?void 0:e.filter)?void 0:n.event)?void 0:o.toLocaleLowerCase();return"*"===s||s===(null==(h=null==t?void 0:t.event)?void 0:h.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:s,commit_timestamp:r,type:i,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:s,commit_timestamp:r,eventType:i,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,s)})}_isClosed(){return this.state===i.closed}_isJoined(){return this.state===i.joined}_isJoining(){return this.state===i.joining}_isLeaving(){return this.state===i.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){let r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){let s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var r;return!((null==(r=e.type)?void 0:r.toLocaleLowerCase())===s&&x.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(n.close,{},e)}_onError(e){this._on(n.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=i.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=j(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=j(e.columns,e.old_record)),t}}let D=()=>{},N=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class B{constructor(e,t){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=D,this.ref=0,this.logger=D,this.conn=null,this.sendBuffer=[],this.serializer=new w,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${a.websocket}`,this.httpEndpoint=A(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let i=null==(r=null==t?void 0:t.params)?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new k(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=_.k),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case r.connecting:return o.Connecting;case r.open:return o.Open;case r.closing:return o.Closing;default:return o.Closed}}isConnected(){return this.connectionState()===o.Open}channel(e,t={config:{}}){let s=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===s);if(r)return r;{let s=new x(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){let{topic:t,event:s,payload:r,ref:i}=e,n=()=>{this.encode(e,e=>{var t;null==(t=this.conn)||t.send(e)})};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:"realtime-js/2.11.15"}),e.joinedOnce&&e._isJoined()&&e._push(n.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected())return void this.heartbeatCallback("disconnected");if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null==(e=this.conn)||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:i}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${i&&"("+i+")"||""}`,r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,i)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(n.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let s=e.match(/\?/)?"&":"?",r=new URLSearchParams(t);return`${e}${s}${r}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([N],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class M extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function H(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class I extends M{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class J extends M{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let F=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},z=()=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(s.bind(s,92410))).Response:Response}),K=e=>{if(Array.isArray(e))return e.map(e=>K(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,s])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=K(s)}),t};var G=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})};let q=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),V=(e,t,s)=>G(void 0,void 0,void 0,function*(){e instanceof(yield z())&&!(null==s?void 0:s.noResolveJson)?e.json().then(s=>{t(new I(q(s),e.status||500))}).catch(e=>{t(new J(q(e),e))}):t(new J(q(e),e))}),W=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),s))};function Q(e,t,s,r,i,n){return G(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(s,W(t,r,i,n)).then(e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>V(e,o,r))})})}function X(e,t,s,r){return G(this,void 0,void 0,function*(){return Q(e,"GET",t,s,r)})}function Y(e,t,s,r,i){return G(this,void 0,void 0,function*(){return Q(e,"POST",t,r,i,s)})}function Z(e,t,s,r,i){return G(this,void 0,void 0,function*(){return Q(e,"DELETE",t,r,i,s)})}var ee=s(49641).Buffer,et=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})};let es={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},er={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class ei{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=F(r)}uploadOrUpdate(e,t,s,r){return et(this,void 0,void 0,function*(){try{let i,n=Object.assign(Object.assign({},er),r),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&s instanceof Blob?((i=new FormData).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o)),i.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?((i=s).append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o))):(i=s,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));let h=this._removeEmptyFolders(t),l=this._getFinalPath(h),c=yield this.fetch(`${this.url}/object/${l}`,Object.assign({method:e,body:i,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield c.json();if(c.ok)return{data:{path:h,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(e){if(H(e))return{data:null,error:e};throw e}})}upload(e,t,s){return et(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return et(this,void 0,void 0,function*(){let i=this._removeEmptyFolders(e),n=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e,t=Object.assign({upsert:er.upsert},r),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s).append("cacheControl",t.cacheControl):(e=s,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),h=yield o.json();if(o.ok)return{data:{path:i,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(H(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return et(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e),r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");let i=yield Y(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),n=new URL(this.url+i.url),a=n.searchParams.get("token");if(!a)throw new M("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}update(e,t,s){return et(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return et(this,void 0,void 0,function*(){try{return{data:yield Y(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}copy(e,t,s){return et(this,void 0,void 0,function*(){try{return{data:{path:(yield Y(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,s){return et(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=yield Y(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,s){return et(this,void 0,void 0,function*(){try{let r=yield Y(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null})),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}download(e,t){return et(this,void 0,void 0,function*(){let s=void 0!==(null==t?void 0:t.transform),r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=r?`?${r}`:"";try{let t=this._getFinalPath(e),r=yield X(this.fetch,`${this.url}/${s?"render/image/authenticated":"object"}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}info(e){return et(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield X(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:K(e),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}exists(e){return et(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,s,r){return G(this,void 0,void 0,function*(){return Q(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(H(e)&&e instanceof J){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let s=this._getFinalPath(e),r=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&r.push(i);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${s}${o}`)}}}remove(e){return et(this,void 0,void 0,function*(){try{return{data:yield Z(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}list(e,t,s){return et(this,void 0,void 0,function*(){try{let r=Object.assign(Object.assign(Object.assign({},es),t),{prefix:e||""});return{data:yield Y(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ee?ee.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let en={"X-Client-Info":"storage-js/2.7.1"};var ea=function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})};class eo{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},en),t),this.fetch=F(s)}listBuckets(){return ea(this,void 0,void 0,function*(){try{return{data:yield X(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}getBucket(e){return ea(this,void 0,void 0,function*(){try{return{data:yield X(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return ea(this,void 0,void 0,function*(){try{return{data:yield Y(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return ea(this,void 0,void 0,function*(){try{return{data:yield function(e,t,s,r,i){return G(this,void 0,void 0,function*(){return Q(e,"PUT",t,r,void 0,s)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}emptyBucket(e){return ea(this,void 0,void 0,function*(){try{return{data:yield Y(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}deleteBucket(e){return ea(this,void 0,void 0,function*(){try{return{data:yield Z(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(H(e))return{data:null,error:e};throw e}})}}class eh extends eo{constructor(e,t={},s){super(e,t,s)}from(e){return new ei(this.url,this.headers,e,this.fetch)}}let el="";el="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let ec={headers:{"X-Client-Info":`supabase-js-${el}/2.50.3`}},eu={schema:"public"},ed={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ef={};var ep=s(92410);let em=e=>{let t;return t=e||("undefined"==typeof fetch?ep.default:fetch),(...e)=>t(...e)},eg=()=>"undefined"==typeof Headers?ep.Headers:Headers,ev=(e,t,s)=>{let r=em(s),i=eg();return(s,n)=>(function(e,t,s,r){return new(s||(s=Promise))(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!=(a=yield t())?a:e,h=new i(null==n?void 0:n.headers);return h.has("apikey")||h.set("apikey",e),h.has("Authorization")||h.set("Authorization",`Bearer ${o}`),r(s,Object.assign(Object.assign({},n),{headers:h}))})};var eb=s(44761);class ey extends eb.UJ{constructor(e){super(e)}}class e_{constructor(e,t,s){var r,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,h=function(e,t){var s,r;let{db:i,auth:n,realtime:a,global:o}=e,{db:h,auth:l,realtime:c,global:u}=t,d={db:Object.assign(Object.assign({},h),i),auth:Object.assign(Object.assign({},l),n),realtime:Object.assign(Object.assign({},c),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!=(s=null==u?void 0:u.headers)?s:{}),null!=(r=null==o?void 0:o.headers)?r:{})}),accessToken:()=>{var e,t,s,r;return e=this,t=void 0,r=function*(){return""},new(s=void 0,s=Promise)(function(i,n){function a(e){try{h(r.next(e))}catch(e){n(e)}}function o(e){try{h(r.throw(e))}catch(e){n(e)}}function h(e){var t;e.done?i(e.value):((t=e.value)instanceof s?t:new s(function(e){e(t)})).then(a,o)}h((r=r.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:eu,realtime:ef,auth:Object.assign(Object.assign({},ed),{storageKey:o}),global:ec});this.storageKey=null!=(r=h.auth.storageKey)?r:"",this.headers=null!=(i=h.global.headers)?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!=(n=h.auth)?n:{},this.headers,h.global.fetch),this.fetch=ev(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new p(new URL("rest/v1",a).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new f.FS(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new eh(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,s,r,i,n;return s=this,r=void 0,i=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:s}=yield this.auth.getSession();return null!=(t=null==(e=s.session)?void 0:e.access_token)?t:null},new(i||(i=Promise))(function(e,t){function a(e){try{h(n.next(e))}catch(e){t(e)}}function o(e){try{h(n.throw(e))}catch(e){t(e)}}function h(t){var s;t.done?e(t.value):((s=t.value)instanceof i?s:new i(function(e){e(s)})).then(a,o)}h((n=n.apply(s,r||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:n,lock:a,debug:o},h,l){let c={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new ey({url:this.authUrl.href,headers:Object.assign(Object.assign({},c),h),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:n,lock:a,debug:o,fetch:l,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new B(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==s?this.changedAccessToken=s:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let ew=(e,t,s)=>new e_(e,t,s)},83280:function(e,t,s){var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=r(s(57156));class n extends i.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let s=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let i="";"plain"===r?i="pl":"phrase"===r?i="ph":"websearch"===r&&(i="w");let n=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){let r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}t.default=n},85171:(e,t,s)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let r=s(10182);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${r.version}`}},92410:(e,t,s)=>{s.r(t),s.d(t,{Headers:()=>a,Request:()=>o,Response:()=>h,default:()=>n,fetch:()=>i});var r=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==s.g)return s.g;throw Error("unable to locate global object")}();let i=r.fetch,n=r.fetch.bind(r),a=r.Headers,o=r.Request,h=r.Response}}]);