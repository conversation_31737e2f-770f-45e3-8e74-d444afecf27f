"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1842],{8032:(e,t,r)=>{r.d(t,{Bu:()=>u,ou:()=>h,a:()=>c,ns:()=>d,Kc:()=>o,Bb:()=>s,M9:()=>g});var a=r(42099);class n{set(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60;this.cache.set(e,{data:t,timestamp:Date.now(),ttl:1e3*r})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>t.ttl?(this.cache.delete(e),null):t.data:null}delete(e){this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,r]of Array.from(this.cache.entries()))e-r.timestamp>r.ttl&&this.cache.delete(t)}constructor(){this.cache=new Map}}let l=new n;async function i(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:60,a=l.get(e);if(a)return a;let n=await t();return l.set(e,n,r),n}async function s(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:50;return i("admin-articles-all-".concat(e),async()=>{let{data:t,error:r}=await a.ND.from("articles").select("\n        id,\n        title,\n        slug,\n        excerpt,\n        featured_image_url,\n        published_at,\n        created_at,\n        status,\n        reading_time,\n        author,\n        tags,\n        featured\n      ").order("created_at",{ascending:!1}).limit(e);if(r)throw Error("خطأ في جلب المقالات للأدمن: ".concat(r.message));return t},60)}async function c(e){let{error:t}=await a.ND.from("articles").delete().eq("id",e);if(t)throw Error("خطأ في حذف المقال: ".concat(t.message))}async function o(){return i("ai-tools-all",async()=>{let{data:e,error:t}=await a.ND.from("ai_tools").select("\n        id,\n        name,\n        slug,\n        description,\n        category,\n        website_url,\n        logo_url,\n        pricing,\n        rating,\n        features,\n        status,\n        featured,\n        created_at\n      ").in("status",["published","active"]).order("rating",{ascending:!1}).order("created_at",{ascending:!1});if(t)throw Error("خطأ في جلب أدوات الذكاء الاصطناعي: ".concat(t.message));return e},1800)}async function d(e){let{data:t,error:r}=await a.ND.from("ai_tools").select("*").eq("id",e).single();if(r)throw Error("خطأ في جلب أداة الذكاء الاصطناعي: ".concat(r.message));return t}async function u(e){let{data:t,error:r}=await a.ND.from("ai_tools").insert([e]).select().single();if(r)throw Error("خطأ في إنشاء أداة الذكاء الاصطناعي: ".concat(r.message));return t}async function g(e,t){let{data:r,error:n}=await a.ND.from("ai_tools").update({...t,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(n)throw Error("خطأ في تحديث أداة الذكاء الاصطناعي: ".concat(n.message));return r}async function h(e){let{error:t}=await a.ND.from("ai_tools").delete().eq("id",e);if(t)throw Error("خطأ في حذف أداة الذكاء الاصطناعي: ".concat(t.message))}},15504:(e,t,r)=>{r.d(t,{Am:()=>l,H1:()=>c,JY:()=>n,dT:()=>i,nt:()=>s,o:()=>o});var a=r(42099);async function n(e){try{let t=a.ND.from("ads").select("*").order("priority",{ascending:!1});(null==e?void 0:e.enabled)!==void 0&&(t=t.eq("enabled",e.enabled)),(null==e?void 0:e.position)&&(t=t.eq("position",e.position)),(null==e?void 0:e.network)&&(t=t.eq("network",e.network)),(null==e?void 0:e.ad_type)&&(t=t.eq("ad_type",e.ad_type));let{data:r,error:n}=await t;if(n)return console.error("Error fetching advanced ads:",n),[];let l=r||[];(null==e?void 0:e.target_page)&&(l=l.filter(t=>{let r=t.target_pages||[];return r.includes("*")||r.some(t=>{var r;return!!(t===e.target_page||t.endsWith("*")&&(null==(r=e.target_page)?void 0:r.startsWith(t.slice(0,-1))))})}));let i=new Date,s=i.getDay(),c=i.getHours();return l=l.filter(e=>!(e.start_date&&new Date(e.start_date)>i||e.end_date&&new Date(e.end_date)<i)&&(!e.schedule_days||!(e.schedule_days.length>0)||!!e.schedule_days.includes(s))&&(!e.schedule_hours||!(e.schedule_hours.length>0)||!!e.schedule_hours.includes(c)))}catch(e){return console.error("Error in getAdvancedAds:",e),[]}}async function l(e){try{let t={...e,start_date:e.start_date&&""!==e.start_date.trim()?e.start_date:null,end_date:e.end_date&&""!==e.end_date.trim()?e.end_date:null,max_impressions:e.max_impressions||null,max_clicks:e.max_clicks||null,frequency_cap:e.frequency_cap||null,target_pages:Array.isArray(e.target_pages)?e.target_pages:["*"],target_devices:Array.isArray(e.target_devices)?e.target_devices:["desktop","mobile","tablet"],target_countries:Array.isArray(e.target_countries)?e.target_countries:[],schedule_days:Array.isArray(e.schedule_days)?e.schedule_days:[0,1,2,3,4,5,6],schedule_hours:Array.isArray(e.schedule_hours)?e.schedule_hours:[],tags:Array.isArray(e.tags)?e.tags:[]},{data:r,error:n}=await a.ND.from("ads").insert([t]).select().single();if(n)return console.error("Error creating advanced ad:",n),null;return r}catch(e){return console.error("Error in createAdvancedAd:",e),null}}async function i(e,t){try{let r={...t,start_date:void 0!==t.start_date?t.start_date&&""!==t.start_date.trim()?t.start_date:null:void 0,end_date:void 0!==t.end_date?t.end_date&&""!==t.end_date.trim()?t.end_date:null:void 0,max_impressions:void 0!==t.max_impressions?t.max_impressions||null:void 0,max_clicks:void 0!==t.max_clicks?t.max_clicks||null:void 0,frequency_cap:void 0!==t.frequency_cap?t.frequency_cap||null:void 0,updated_at:new Date().toISOString()},{data:n,error:l}=await a.ND.from("ads").update(r).eq("id",e).select().single();if(l)return console.error("Error updating advanced ad:",l),null;return n}catch(e){return console.error("Error in updateAdvancedAd:",e),null}}async function s(e){try{let{error:t}=await a.ND.from("ads").delete().eq("id",e);if(t)return console.error("Error deleting advanced ad:",t),!1;return!0}catch(e){return console.error("Error in deleteAdvancedAd:",e),!1}}async function c(e,t){try{let r=a.ND.from("ad_templates").select("*").eq("is_public",!0).order("usage_count",{ascending:!1});e&&(r=r.eq("category",e)),t&&(r=r.eq("network",t));let{data:n,error:l}=await r;if(l)return console.error("Error fetching ad templates:",l),[];return n||[]}catch(e){return console.error("Error in getAdTemplates:",e),[]}}async function o(e,t){try{let{data:r,error:n}=await a.ND.from("ad_templates").select("*").eq("id",e).single();if(n||!r)return console.error("Error fetching template:",n),null;let i=r.html_template||"",s=r.css_template||"",c=r.javascript_template||"";Object.entries(t).forEach(e=>{let[t,r]=e,a="{{".concat(t,"}}");i=i.replace(RegExp(a,"g"),String(r)),s=s.replace(RegExp(a,"g"),String(r)),c=c.replace(RegExp(a,"g"),String(r))});let o={name:t.name||r.name,description:"Created from template: ".concat(r.name),ad_type:r.category||"banner",ad_format:"html",network:r.network,html_content:i,css_content:s,javascript_content:c,position:t.position||"header",z_index:t.z_index||1e3,target_pages:t.target_pages||["*"],target_devices:["desktop","mobile","tablet"],target_countries:[],schedule_days:[0,1,2,3,4,5,6],schedule_hours:[],enabled:!0,priority:t.priority||5,responsive_breakpoints:{mobile:768,tablet:1024},animation_duration:300,hover_effects:{},ab_test_weight:100,tags:[r.category,r.network].filter(Boolean)},d=await l(o);return d&&await a.ND.from("ad_templates").update({usage_count:r.usage_count+1}).eq("id",e),d}catch(e){return console.error("Error in createAdFromTemplate:",e),null}}},29729:(e,t,r)=>{r.d(t,{$d:()=>i});var a=r(85025),n=r.n(a);let l={allowedTags:["h1","h2","h3","h4","h5","h6","p","br","hr","ul","ol","li","strong","em","b","i","a","img","blockquote","code","pre"],allowedAttributes:{a:["href","target","rel"],img:["src","alt","width","height"]},allowedSchemes:["http","https","mailto"],allowedSchemesByTag:{img:["http","https","data"]},allowProtocolRelative:!1,enforceHtmlBoundary:!0};function i(e){let t=function(e){if(!e||"string"!=typeof e)return"";let t=!1,r=n()(e,l);return(r=(r=(r=(r=r.replace(/<h1([^>]*)>([\s\S]*?)<\/h1>/gi,(e,r,a)=>t?"<h2>".concat(a.trim(),"</h2>"):(t=!0,"<h1>".concat(a.trim(),"</h1>")))).replace(/<p[^>]*>\s*<\/p>/gi,"").replace(/<(div|span)[^>]*>\s*<\/\1>/gi,"").replace(/\s+/g," ").replace(/^\s+|\s+$/gm,"").replace(/\n\s*\n\s*\n/g,"\n\n")).replace(/<a([^>]*href="[^"]*"[^>]*)>/gi,(e,t)=>{if(t.includes("http")&&!t.includes("target="))if(!t.includes("rel="))return"<a".concat(t,' target="_blank" rel="noopener noreferrer">');else return"<a".concat(t,' target="_blank">');return e})).replace(/<img([^>]*)>/gi,(e,t)=>t.includes("alt=")?e:"<img".concat(t,' alt="صورة">'))).trim()}(e),r=function(e,t){let r=e.length,a=t.length,n=r-a,l=r>0?Math.round(n/r*100):0,i=(e.match(/<h1[^>]*>/gi)||[]).length,s=(t.match(/<h1[^>]*>/gi)||[]).length,c=(e.match(/<script[^>]*>[\s\S]*?<\/script>/gi)||[]).length,o=(e.match(/<iframe[^>]*>[\s\S]*?<\/iframe>/gi)||[]).length,d=(e.match(/<object[^>]*>[\s\S]*?<\/object>/gi)||[]).length,u=(e.match(/<embed[^>]*>/gi)||[]).length;return{originalLength:r,cleanedLength:a,reduction:n,reductionPercentage:l,h1Converted:i-s,removedElements:{scripts:c,iframes:o,objects:d,embeds:u,total:c+o+d+u}}}(e,t),a=function(e){let t=[],r=[];if(!e||0===e.trim().length)return t.push("المحتوى فارغ بعد التنظيف"),{isValid:!1,issues:t,warnings:r};let a=(e.match(/<h1[^>]*>/gi)||[]).length;0===a?r.push("لا يوجد عنوان رئيسي (H1) في المحتوى"):a>1&&t.push("يوجد ".concat(a," عناوين رئيسية (H1) - يجب أن يكون هناك عنوان واحد فقط")),e.replace(/<[^>]*>/g,"").trim().length<100&&r.push("المحتوى قصير جداً (أقل من 100 حرف)");let n=e.match(/<img[^>]*(?!.*alt=)[^>]*>/gi);return n&&n.length>0&&r.push("يوجد ".concat(n.length," صور بدون نص بديل (alt text)")),{isValid:0===t.length,issues:t,warnings:r}}(t);return{originalContent:e,cleanedContent:t,stats:r,validation:a,hasChanges:e!==t}}},66050:(e,t,r)=>{r.d(t,{$n:()=>s,JR:()=>n,T6:()=>i,Zu:()=>a,qR:()=>c});let a="G-X8ZRRZX2EQ",n=e=>{try{window.gtag&&window.gtag("config",a,{page_path:e})}catch(e){}},l=e=>{let{action:t,category:r,label:a,value:n}=e;window.gtag&&window.gtag("event",t,{event_category:r,event_label:a,value:n})},i=(e,t)=>{l({action:"click_ad",category:"ads",label:"".concat(e," - ").concat(t)})},s=e=>{l({action:"newsletter_subscribe",category:"engagement",label:e})},c=e=>{l({action:"page_scroll",category:"engagement",label:"".concat(e,"%"),value:e})}}}]);