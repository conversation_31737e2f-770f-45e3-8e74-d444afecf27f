"use strict";exports.id=1758,exports.ids=[1758],exports.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33243:(e,t,r)=>{r.d(t,{kk:()=>o,t8:()=>u});let n="https://www.tflash.site";function o(e,t,r){if(!t||""===t.trim())return n;let o=a(t),u="";switch(e){case"article":u=`articles/${o}`;break;case"ai-tool":u=`ai-tools/${o}`;break;case"category":u=r?a(r):o;break;case"page":u="home"===o?"":`page/${o}`;break;default:u=o}let l=function(e){let t=e.startsWith("/")?e.slice(1):e,r=(""===t?"":t.replace(/\/$/,"")).split("?")[0];return`${n}/${r||""}`}(u);return!function(e){try{let t=new URL(e);if("https:"!==t.protocol||!t.hostname.startsWith("www.")||!t.hostname.endsWith("tflash.site")||"/"!==t.pathname&&t.pathname.endsWith("/")||t.search||t.hash)return!1;return!0}catch{return!1}}(l)?n:l}function a(e){return e&&"string"==typeof e?e.toLowerCase().trim().replace(/[أإآا]/g,"a").replace(/[ة]/g,"h").replace(/[ي]/g,"y").replace(/[و]/g,"w").replace(/[ر]/g,"r").replace(/[ت]/g,"t").replace(/[ن]/g,"n").replace(/[م]/g,"m").replace(/[ل]/g,"l").replace(/[ك]/g,"k").replace(/[ج]/g,"j").replace(/[ح]/g,"h").replace(/[خ]/g,"kh").replace(/[د]/g,"d").replace(/[ذ]/g,"th").replace(/[س]/g,"s").replace(/[ش]/g,"sh").replace(/[ص]/g,"s").replace(/[ض]/g,"d").replace(/[ط]/g,"t").replace(/[ظ]/g,"th").replace(/[ع]/g,"a").replace(/[غ]/g,"gh").replace(/[ف]/g,"f").replace(/[ق]/g,"q").replace(/[ه]/g,"h").replace(/[ز]/g,"z").replace(/[ب]/g,"b").replace(/[ء]/g,"").replace(/\s+/g,"-").replace(/_/g,"-").replace(/[^\x00-\x7F]/g,"").replace(/[^a-z0-9-]/g,"").replace(/-+/g,"-").replace(/^-+|-+$/g,"")||"page":""}function u(e){return{alternates:{canonical:e}}}},39916:(e,t,r)=>{var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}})},48976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return o}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function o(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,u.isNextRouterError)(t)||(0,a.isBailoutToCSRError)(t)||(0,i.isDynamicServerError)(t)||(0,l.isDynamicPostpone)(t)||(0,o.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),o=r(52637),a=r(51846),u=r(31162),l=r(84971),i=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return f},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return c},permanentRedirect:function(){return i},redirect:function(){return l}});let n=r(52836),o=r(49026),a=r(19121).actionAsyncStorage;function u(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let a=Object.defineProperty(Error(o.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return a.digest=o.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",a}function l(e,t){var r;throw null!=t||(t=(null==a||null==(r=a.getStore())?void 0:r.isAction)?o.RedirectType.push:o.RedirectType.replace),u(e,t,n.RedirectStatusCode.TemporaryRedirect)}function i(e,t){throw void 0===t&&(t=o.RedirectType.replace),u(e,t,n.RedirectStatusCode.PermanentRedirect)}function c(e){return(0,o.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function f(e){if(!(0,o.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return o.RedirectType},forbidden:function(){return u.forbidden},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return l.unauthorized},unstable_rethrow:function(){return i.unstable_rethrow}});let n=r(86897),o=r(49026),a=r(62765),u=r(48976),l=r(70899),i=r(163);class c extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new c}delete(){throw new c}set(){throw new c}sort(){throw new c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};