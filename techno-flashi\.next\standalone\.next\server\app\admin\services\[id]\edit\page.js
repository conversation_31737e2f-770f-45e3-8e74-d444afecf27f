(()=>{var e={};e.id=6348,e.ids=[6348],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3342:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(60687),i=s(43210),a=s(16189),n=s(85814),o=s.n(n);function l({params:e}){let r=(0,a.useRouter)(),[s,n]=(0,i.useState)(!0),[l,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(null),[m,p]=(0,i.useState)(""),[x,h]=(0,i.useState)({name:"",description:"",short_description:"",category:"general",icon_url:"",image_url:"",pricing_type:"custom",pricing_amount:void 0,pricing_currency:"USD",status:"active",featured:!1,cta_text:"تعرف أكثر",cta_link:"",display_order:0,tags:[],features:[]}),[g,f]=(0,i.useState)(""),[b,y]=(0,i.useState)(""),v=async e=>{e.preventDefault(),d(!0);try{let e=g.split(",").map(e=>e.trim()).filter(e=>e),s=b.split("\n").map(e=>e.trim()).filter(e=>e),t={...x,tags:e,features:s,pricing_amount:x.pricing_amount||null},i=await fetch(`/api/services/${m}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!i.ok){let e=await i.json();throw Error(e.error||"فشل في تحديث الخدمة")}await i.json(),r.push("/admin/services")}catch(e){console.error("Error updating service:",e),alert("حدث خطأ في تحديث الخدمة: "+e.message)}finally{d(!1)}},j=e=>{let{name:r,value:s,type:t}=e.target;if("checkbox"===t){let s=e.target.checked;h(e=>({...e,[r]:s}))}else"number"===t?h(e=>({...e,[r]:s?parseFloat(s):void 0})):h(e=>({...e,[r]:s}))};return s?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"جاري تحميل بيانات الخدمة..."})]})}):c?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"تعديل الخدمة"}),(0,t.jsxs)("p",{className:"text-gray-600 mt-2",children:["تعديل بيانات الخدمة: ",c.name]})]}),(0,t.jsxs)("div",{className:"flex space-x-4 space-x-reverse",children:[(0,t.jsx)(o(),{href:`/services/${c.id}`,target:"_blank",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200",children:"معاينة الخدمة"}),(0,t.jsx)(o(),{href:"/admin/services",className:"bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200",children:"العودة للقائمة"})]})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-sm",children:(0,t.jsxs)("form",{onSubmit:v,className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم الخدمة *"}),(0,t.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:x.name,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"أدخل اسم الخدمة"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"التصنيف"}),(0,t.jsxs)("select",{id:"category",name:"category",value:x.category,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,t.jsx)("option",{value:"general",children:"عام"}),(0,t.jsx)("option",{value:"web-development",children:"تطوير مواقع"}),(0,t.jsx)("option",{value:"ai-solutions",children:"حلول الذكاء الاصطناعي"}),(0,t.jsx)("option",{value:"mobile-apps",children:"تطبيقات الجوال"}),(0,t.jsx)("option",{value:"consulting",children:"استشارات"}),(0,t.jsx)("option",{value:"design",children:"تصميم"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"short_description",className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف المختصر"}),(0,t.jsx)("input",{type:"text",id:"short_description",name:"short_description",value:x.short_description,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"وصف مختصر للخدمة (اختياري)",maxLength:500})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف التفصيلي *"}),(0,t.jsx)("textarea",{id:"description",name:"description",required:!0,rows:6,value:x.description,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"وصف تفصيلي للخدمة"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"icon_url",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط الأيقونة"}),(0,t.jsx)("input",{type:"url",id:"icon_url",name:"icon_url",value:x.icon_url,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com/icon.png"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"image_url",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط الصورة الرئيسية"}),(0,t.jsx)("input",{type:"url",id:"image_url",name:"image_url",value:x.image_url,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com/image.jpg"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"pricing_type",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع التسعير"}),(0,t.jsxs)("select",{id:"pricing_type",name:"pricing_type",value:x.pricing_type,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,t.jsx)("option",{value:"free",children:"مجاني"}),(0,t.jsx)("option",{value:"paid",children:"مدفوع"}),(0,t.jsx)("option",{value:"custom",children:"حسب الطلب"})]})]}),"paid"===x.pricing_type&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"pricing_amount",className:"block text-sm font-medium text-gray-700 mb-2",children:"السعر"}),(0,t.jsx)("input",{type:"number",id:"pricing_amount",name:"pricing_amount",min:"0",step:"0.01",value:x.pricing_amount||"",onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"0.00"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"pricing_currency",className:"block text-sm font-medium text-gray-700 mb-2",children:"العملة"}),(0,t.jsxs)("select",{id:"pricing_currency",name:"pricing_currency",value:x.pricing_currency,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,t.jsx)("option",{value:"USD",children:"دولار أمريكي (USD)"}),(0,t.jsx)("option",{value:"EUR",children:"يورو (EUR)"}),(0,t.jsx)("option",{value:"EGP",children:"جنيه مصري (EGP)"}),(0,t.jsx)("option",{value:"SAR",children:"ريال سعودي (SAR)"})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"cta_text",className:"block text-sm font-medium text-gray-700 mb-2",children:"نص زر العمل"}),(0,t.jsx)("input",{type:"text",id:"cta_text",name:"cta_text",value:x.cta_text,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"تعرف أكثر"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"cta_link",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط زر العمل"}),(0,t.jsx)("input",{type:"text",id:"cta_link",name:"cta_link",value:x.cta_link,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"/page/contact-us"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"tags",className:"block text-sm font-medium text-gray-700 mb-2",children:"العلامات (مفصولة بفواصل)"}),(0,t.jsx)("input",{type:"text",id:"tags",value:g,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"تقنية, ويب, تطوير"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"features",className:"block text-sm font-medium text-gray-700 mb-2",children:"المميزات (كل ميزة في سطر منفصل)"}),(0,t.jsx)("textarea",{id:"features",rows:4,value:b,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"ميزة 1 ميزة 2 ميزة 3"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"الحالة"}),(0,t.jsxs)("select",{id:"status",name:"status",value:x.status,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,t.jsx)("option",{value:"active",children:"نشط"}),(0,t.jsx)("option",{value:"inactive",children:"غير نشط"}),(0,t.jsx)("option",{value:"draft",children:"مسودة"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"display_order",className:"block text-sm font-medium text-gray-700 mb-2",children:"ترتيب العرض"}),(0,t.jsx)("input",{type:"number",id:"display_order",name:"display_order",min:"0",value:x.display_order,onChange:j,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"0"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"featured",name:"featured",checked:x.featured,onChange:j,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"featured",className:"mr-2 block text-sm text-gray-900",children:"خدمة مميزة"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 space-x-reverse pt-6 border-t",children:[(0,t.jsx)(o(),{href:"/admin/services",className:"bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors duration-200",children:"إلغاء"}),(0,t.jsx)("button",{type:"submit",disabled:l,className:"bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:l?"جاري الحفظ...":"حفظ التغييرات"})]})]})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-red-600 mb-4",children:"لم يتم العثور على الخدمة"}),(0,t.jsx)(o(),{href:"/admin/services",className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90",children:"العودة للقائمة"})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,r,s)=>{"use strict";s.d(r,{ProtectedRoute:()=>n});var t=s(60687),i=s(63213),a=s(16189);function n({children:e}){let{user:r,loading:s}=(0,i.A)();return((0,a.useRouter)(),s)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,t.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,t.jsx)(t.Fragment,{children:e}):null}s(43210)},23412:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\services\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\[id]\\edit\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,r,s)=>{Promise.resolve().then(s.bind(s,67083))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,r,s)=>{"use strict";s.d(r,{ProtectedRoute:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},69019:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),i=s(48088),a=s(88170),n=s.n(a),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d={children:["",{children:["admin",{children:["services",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23412)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\[id]\\edit\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/services/[id]/edit/page",pathname:"/admin/services/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},92177:(e,r,s)=>{Promise.resolve().then(s.bind(s,23412))},94735:e=>{"use strict";e.exports=require("events")},97579:(e,r,s)=>{Promise.resolve().then(s.bind(s,20769))},98801:(e,r,s)=>{Promise.resolve().then(s.bind(s,3342))},99111:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>a});var t=s(37413),i=s(67083);function a({children:e}){return(0,t.jsx)(i.ProtectedRoute,{children:(0,t.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}s(31240)}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,6437,2675,3595],()=>s(69019));module.exports=t})();