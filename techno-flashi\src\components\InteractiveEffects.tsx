'use client';

import { useEffect, useRef } from 'react';

interface InteractiveEffectsProps {
  target: 'header' | 'hero';
  className?: string;
}

export function InteractiveEffects({ target, className = '' }: InteractiveEffectsProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement[]>([]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // إنشاء جسيمات خفيفة
    const createParticles = () => {
      const particleCount = target === 'header' ? 3 : 5;
      
      for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = `absolute pointer-events-none transition-all duration-1000 ease-out ${
          target === 'header' 
            ? 'w-1 h-1 bg-purple-400/20 rounded-full' 
            : 'w-2 h-2 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-full'
        }`;
        
        // موضع عشوائي
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.opacity = '0';
        
        container.appendChild(particle);
        particlesRef.current.push(particle);
      }
    };

    // تحريك الجسيمات مع الماوس
    const handleMouseMove = (e: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width) * 100;
      const y = ((e.clientY - rect.top) / rect.height) * 100;

      particlesRef.current.forEach((particle, index) => {
        const delay = index * 50;
        const offsetX = (Math.random() - 0.5) * 20;
        const offsetY = (Math.random() - 0.5) * 20;
        
        setTimeout(() => {
          particle.style.left = Math.max(0, Math.min(100, x + offsetX)) + '%';
          particle.style.top = Math.max(0, Math.min(100, y + offsetY)) + '%';
          particle.style.opacity = '1';
        }, delay);
      });
    };

    // إخفاء الجسيمات عند مغادرة الماوس
    const handleMouseLeave = () => {
      particlesRef.current.forEach((particle, index) => {
        setTimeout(() => {
          particle.style.opacity = '0';
        }, index * 30);
      });
    };

    // تأثير الموجات عند النقر
    const handleClick = (e: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      const ripple = document.createElement('div');
      ripple.className = `absolute pointer-events-none rounded-full ${
        target === 'header'
          ? 'bg-purple-400/10 border border-purple-400/20'
          : 'bg-gradient-to-r from-purple-400/10 to-pink-400/10 border border-purple-400/30'
      }`;
      
      ripple.style.left = x + 'px';
      ripple.style.top = y + 'px';
      ripple.style.width = '0px';
      ripple.style.height = '0px';
      ripple.style.transform = 'translate(-50%, -50%)';
      
      container.appendChild(ripple);

      // تحريك الموجة
      requestAnimationFrame(() => {
        ripple.style.width = '100px';
        ripple.style.height = '100px';
        ripple.style.opacity = '0';
        ripple.style.transition = 'all 0.6s ease-out';
      });

      // إزالة الموجة
      setTimeout(() => {
        container.removeChild(ripple);
      }, 600);
    };

    createParticles();
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseleave', handleMouseLeave);
    container.addEventListener('click', handleClick);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseleave', handleMouseLeave);
      container.removeEventListener('click', handleClick);
      
      // تنظيف الجسيمات
      particlesRef.current.forEach(particle => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      });
      particlesRef.current = [];
    };
  }, [target]);

  return (
    <div 
      ref={containerRef}
      className={`absolute inset-0 overflow-hidden ${className}`}
      style={{ zIndex: target === 'header' ? 1 : 0 }}
    />
  );
}

// مكون تأثيرات الأزرار
export function ButtonHoverEffect({ children, className = '', variant = 'primary' }: {
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary';
}) {
  return (
    <div className={`group relative overflow-hidden ${className}`}>
      {/* تأثير الخلفية المتحركة */}
      <div className={`absolute inset-0 transition-all duration-300 ease-out transform scale-x-0 group-hover:scale-x-100 origin-left ${
        variant === 'primary' 
          ? 'bg-gradient-to-r from-purple-700 to-pink-700' 
          : 'bg-purple-400/10'
      }`} />
      
      {/* تأثير الضوء */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
        <div className={`absolute top-0 left-0 w-full h-full ${
          variant === 'primary'
            ? 'bg-gradient-to-r from-transparent via-white/10 to-transparent'
            : 'bg-gradient-to-r from-transparent via-purple-400/20 to-transparent'
        } transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out`} />
      </div>
      
      {/* المحتوى */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
}

// مكون تأثيرات النص
export function TextGlowEffect({ children, className = '' }: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={`group relative ${className}`}>
      {/* تأثير التوهج */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-purple-600/20 blur-xl" />
      </div>
      
      {/* النص */}
      <div className="relative z-10 transition-all duration-300 group-hover:scale-105">
        {children}
      </div>
    </div>
  );
}

export default InteractiveEffects;
