(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2365],{13987:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n});var a=s(95155),t=s(12115),c=s(35695);function n(){let e=(0,c.useRouter)();return(0,t.useEffect)(()=>{e.replace("/admin/articles/create")},[e]),(0,a.jsx)("div",{className:"min-h-screen bg-dark-background flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-white",children:"جاري إعادة التوجيه إلى الصفحة الجديدة..."})]})})}},42889:(e,r,s)=>{Promise.resolve().then(s.bind(s,13987))}},e=>{var r=r=>e(e.s=r);e.O(0,[3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>r(42889)),_N_E=e.O()}]);