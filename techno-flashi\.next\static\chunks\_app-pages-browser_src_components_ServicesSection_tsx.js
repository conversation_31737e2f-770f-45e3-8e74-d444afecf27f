"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_ServicesSection_tsx"],{

/***/ "(app-pages-browser)/./src/components/ServiceCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ServiceCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServiceCard: () => (/* binding */ ServiceCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ ServiceCard auto */ \n\n\nfunction ServiceCard(param) {\n    let { service, variant = 'default' } = param;\n    const formatPrice = (amount, currency)=>{\n        if (!amount) return null;\n        return new Intl.NumberFormat('ar-EG', {\n            style: 'currency',\n            currency: currency || 'USD',\n            minimumFractionDigits: 0\n        }).format(amount);\n    };\n    const getPricingDisplay = ()=>{\n        switch(service.pricing_type){\n            case 'free':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-green-600 font-bold\",\n                    children: \"مجاني\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 16\n                }, this);\n            case 'paid':\n                return service.pricing_amount ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-primary font-bold\",\n                    children: formatPrice(service.pricing_amount, service.pricing_currency)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-text-description\",\n                    children: \"السعر غير محدد\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 11\n                }, this);\n            case 'custom':\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-text-description\",\n                    children: \"حسب الطلب\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"modern-card overflow-hidden hover-lift\",\n        children: [\n            service.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative h-48 w-full rounded-t-2xl overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: service.image_url,\n                        alt: service.name,\n                        fill: true,\n                        sizes: \"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\",\n                        className: \"object-cover transition-transform duration-500 group-hover:scale-110\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    service.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-4 right-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg\",\n                        children: \"⭐ مميز\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                lineNumber: 44,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: [\n                    service.icon_url && !service.image_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                src: service.icon_url,\n                                alt: service.name,\n                                fill: true,\n                                className: \"object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-text-primary mb-3 text-center\",\n                        children: service.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-description text-center mb-4 leading-relaxed\",\n                        children: service.short_description || service.description.substring(0, 120) + '...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-background-secondary text-text-secondary px-3 py-1 rounded-full text-sm\",\n                            children: service.category\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    service.features && service.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 justify-center\",\n                            children: [\n                                service.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-primary/10 text-primary px-2 py-1 rounded text-xs\",\n                                        children: feature\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this)),\n                                service.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-text-description text-xs\",\n                                    children: [\n                                        \"+\",\n                                        service.features.length - 3,\n                                        \" أكثر\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-4\",\n                        children: getPricingDisplay()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: service.cta_link ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: service.cta_link,\n                            className: \"modern-button text-white px-6 py-3 hover-lift focus-modern min-h-[44px] inline-block\",\n                            \"aria-label\": \"\".concat(service.cta_text, \" - \").concat(service.name),\n                            children: service.cta_text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/services/\".concat(service.id),\n                            className: \"modern-button text-white px-6 py-3 hover-lift focus-modern min-h-[44px] inline-block\",\n                            \"aria-label\": \"\".concat(service.cta_text, \" - \").concat(service.name),\n                            children: service.cta_text\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServiceCard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_c = ServiceCard;\nvar _c;\n$RefreshReg$(_c, \"ServiceCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ServiceCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ServicesSection.tsx":
/*!********************************************!*\
  !*** ./src/components/ServicesSection.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesSection: () => (/* binding */ ServicesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ServiceCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ServiceCard */ \"(app-pages-browser)/./src/components/ServiceCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ ServicesSection auto */ \n\n\nfunction ServicesSection(param) {\n    let { services, title = \"خدماتنا\", showViewAll = true, variant = 'default', maxItems } = param;\n    // تحديد عدد الخدمات المعروضة\n    const displayServices = maxItems ? services.slice(0, maxItems) : services;\n    if (!services || services.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-background-secondary\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-text-primary mb-4\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-description mb-4\",\n                                    children: \"حاليًا، لا نقدم أي خدمات مدفوعة أو منتجات للبيع.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-text-description\",\n                                    children: \"الموقع مخصص لتقديم محتوى تثقيفي وتعليمي مجاني عن تقنيات وأدوات الذكاء الاصطناعي.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-background-secondary\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-text-primary mb-4\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-text-description max-w-2xl mx-auto\",\n                            children: \"نقدم مجموعة متنوعة من الخدمات التقنية المتطورة لمساعدتك في تحقيق أهدافك الرقمية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                    children: displayServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ServiceCard__WEBPACK_IMPORTED_MODULE_2__.ServiceCard, {\n                            service: service,\n                            variant: service.featured ? 'featured' : variant\n                        }, service.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                showViewAll && services.length > (maxItems || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/services\",\n                        className: \"inline-block bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200\",\n                        children: \"عرض جميع الخدمات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, this),\n                services.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-text-description\",\n                                    children: \"خدمة متاحة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.filter((s)=>s.featured).length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-text-description\",\n                                    children: \"خدمة مميزة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.filter((s)=>s.pricing_type === 'free').length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-text-description\",\n                                    children: \"خدمة مجانية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ServicesSection.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c = ServicesSection;\nvar _c;\n$RefreshReg$(_c, \"ServicesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ServicesSection.tsx\n"));

/***/ })

}]);