/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22HeaderAnimatedAd%22%2C%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CSupabaseAdManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22HeaderAnimatedAd%22%2C%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CSupabaseAdManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/AnimatedAdRenderer.tsx */ \"(app-pages-browser)/./src/components/ads/AnimatedAdRenderer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/SupabaseAdManager.tsx */ \"(app-pages-browser)/./src/components/ads/SupabaseAdManager.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/TechnoFlashBanner.tsx */ \"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LatestAIToolsSection.tsx */ \"(app-pages-browser)/./src/components/LatestAIToolsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/NewsletterSubscription.tsx */ \"(app-pages-browser)/./src/components/NewsletterSubscription.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformanceOptimizer.tsx */ \"(app-pages-browser)/./src/components/PerformanceOptimizer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SocialShare.tsx */ \"(app-pages-browser)/./src/components/SocialShare.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SponsorsSection.tsx */ \"(app-pages-browser)/./src/components/SponsorsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/critical-homepage.css */ \"(app-pages-browser)/./src/styles/critical-homepage.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22HeaderAnimatedAd%22%2C%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CSupabaseAdManager.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ads/SupabaseAdManager.tsx":
/*!**************************************************!*\
  !*** ./src/components/ads/SupabaseAdManager.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseFooterAd: () => (/* binding */ SupabaseFooterAd),\n/* harmony export */   SupabaseHeaderAd: () => (/* binding */ SupabaseHeaderAd),\n/* harmony export */   SupabaseInContentAd: () => (/* binding */ SupabaseInContentAd),\n/* harmony export */   SupabaseSidebarAd: () => (/* binding */ SupabaseSidebarAd),\n/* harmony export */   \"default\": () => (/* binding */ SupabaseAdManager),\n/* harmony export */   useSupabaseAds: () => (/* binding */ useSupabaseAds)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase-ads */ \"(app-pages-browser)/./src/lib/supabase-ads.ts\");\n/* __next_internal_client_entry_do_not_use__ default,SupabaseHeaderAd,SupabaseSidebarAd,SupabaseInContentAd,SupabaseFooterAd,useSupabaseAds auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nfunction SupabaseAdManager(param) {\n    let { position, currentPage, className = '', showDebug = false } = param;\n    _s();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentAdIndex, setCurrentAdIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const scriptLoadedRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Set());\n    // Load ads from Supabase\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseAdManager.useEffect\": ()=>{\n            const loadAds = {\n                \"SupabaseAdManager.useEffect.loadAds\": async ()=>{\n                    try {\n                        const fetchedAds = await (0,_lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__.getAdsForPage)(currentPage, position);\n                        setAds(fetchedAds);\n                        setIsLoaded(true);\n                        if (fetchedAds.length > 0) {\n                            console.log(\"Loaded \".concat(fetchedAds.length, \" ads for position: \").concat(position, \", page: \").concat(currentPage));\n                        }\n                    } catch (err) {\n                        console.error('Error loading ads:', err);\n                        setError('Failed to load ads');\n                        setIsLoaded(true);\n                    }\n                }\n            }[\"SupabaseAdManager.useEffect.loadAds\"];\n            loadAds();\n        }\n    }[\"SupabaseAdManager.useEffect\"], [\n        currentPage,\n        position\n    ]);\n    // Load ad script\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseAdManager.useEffect\": ()=>{\n            if (!isLoaded || ads.length === 0) return;\n            const currentAd = ads[currentAdIndex];\n            if (!currentAd || scriptLoadedRef.current.has(currentAd.id)) return;\n            const loadAdScript = {\n                \"SupabaseAdManager.useEffect.loadAdScript\": async ()=>{\n                    try {\n                        // Add delay if specified\n                        if (currentAd.delay_seconds && currentAd.delay_seconds > 0) {\n                            await new Promise({\n                                \"SupabaseAdManager.useEffect.loadAdScript\": (resolve)=>setTimeout(resolve, currentAd.delay_seconds * 1000)\n                            }[\"SupabaseAdManager.useEffect.loadAdScript\"]);\n                        }\n                        // Create and execute script\n                        const script = document.createElement('script');\n                        script.innerHTML = currentAd.script_code;\n                        script.setAttribute('data-ad-id', currentAd.id);\n                        // Add to body\n                        document.body.appendChild(script);\n                        scriptLoadedRef.current.add(currentAd.id);\n                        // Track ad load\n                        await (0,_lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__.trackAdPerformance)(currentAd.id, 'load', currentPage);\n                        console.log(\"Ad loaded: \".concat(currentAd.name, \" (\").concat(currentAd.id, \")\"));\n                        // Track view after 1 second\n                        setTimeout({\n                            \"SupabaseAdManager.useEffect.loadAdScript\": async ()=>{\n                                await (0,_lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__.trackAdPerformance)(currentAd.id, 'view', currentPage);\n                            }\n                        }[\"SupabaseAdManager.useEffect.loadAdScript\"], 1000);\n                    } catch (error) {\n                        console.error('Failed to load ad script:', error);\n                        setError(\"Failed to load ad: \".concat(currentAd.name));\n                    }\n                }\n            }[\"SupabaseAdManager.useEffect.loadAdScript\"];\n            loadAdScript();\n        }\n    }[\"SupabaseAdManager.useEffect\"], [\n        isLoaded,\n        ads,\n        currentAdIndex,\n        currentPage\n    ]);\n    // Handle ad click\n    const handleAdClick = async ()=>{\n        if (ads.length > 0) {\n            const currentAd = ads[currentAdIndex];\n            await (0,_lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__.trackAdPerformance)(currentAd.id, 'click', currentPage);\n            console.log(\"Ad clicked: \".concat(currentAd.name));\n        }\n    };\n    // Rotate ads if multiple available\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SupabaseAdManager.useEffect\": ()=>{\n            if (ads.length <= 1) return;\n            const interval = setInterval({\n                \"SupabaseAdManager.useEffect.interval\": ()=>{\n                    setCurrentAdIndex({\n                        \"SupabaseAdManager.useEffect.interval\": (prev)=>(prev + 1) % ads.length\n                    }[\"SupabaseAdManager.useEffect.interval\"]);\n                }\n            }[\"SupabaseAdManager.useEffect.interval\"], 30000); // Rotate every 30 seconds\n            return ({\n                \"SupabaseAdManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SupabaseAdManager.useEffect\"];\n        }\n    }[\"SupabaseAdManager.useEffect\"], [\n        ads.length\n    ]);\n    // Don't show on admin pages\n    if (currentPage.startsWith('/admin') || currentPage.startsWith('/api')) {\n        return null;\n    }\n    // Debug logging\n    if (showDebug) {\n        console.log(\"SupabaseAdManager: position=\".concat(position, \", currentPage=\").concat(currentPage, \", ads=\").concat(ads.length, \", isLoaded=\").concat(isLoaded));\n    }\n    if (!isLoaded) {\n        return showDebug ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"supabase-ad-loading \".concat(className),\n            style: {\n                padding: '10px',\n                background: '#f8f9fa',\n                border: '1px solid #e9ecef',\n                borderRadius: '4px',\n                textAlign: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: \"Loading ads...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n            lineNumber: 120,\n            columnNumber: 7\n        }, this) : null;\n    }\n    if (error) {\n        return showDebug ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"supabase-ad-error \".concat(className),\n            style: {\n                padding: '10px',\n                background: '#f8d7da',\n                border: '1px solid #f5c6cb',\n                borderRadius: '4px',\n                textAlign: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    \"Error: \",\n                    error\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this) : null;\n    }\n    if (ads.length === 0) {\n        return showDebug ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"supabase-ad-empty \".concat(className),\n            style: {\n                padding: '10px',\n                background: '#fff3cd',\n                border: '1px solid #ffeaa7',\n                borderRadius: '4px',\n                textAlign: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                children: [\n                    \"No ads available for position: \",\n                    position\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 155,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n            lineNumber: 148,\n            columnNumber: 7\n        }, this) : null;\n    }\n    const currentAd = ads[currentAdIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"supabase-ad-container supabase-ad-\".concat(position, \" \").concat(className),\n        \"data-ad-id\": currentAd.id,\n        \"data-ad-name\": currentAd.name,\n        \"data-zone-id\": currentAd.zone_id,\n        onClick: handleAdClick,\n        style: {\n            minHeight: position === 'header' ? '90px' : position === 'sidebar' ? '250px' : position === 'footer' ? '90px' : '200px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            margin: position === 'in-content' ? '20px 0' : '10px 0',\n            position: 'relative',\n            cursor: 'pointer',\n            overflow: 'hidden'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"supabase-ad-content w-full h-full\",\n                children: currentAd.html_code ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    dangerouslySetInnerHTML: {\n                        __html: currentAd.html_code\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"supabase-ad-placeholder text-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-500 mb-2\",\n                            children: currentAd.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400\",\n                            children: [\n                                currentAd.type.toUpperCase(),\n                                \" - Zone: \",\n                                currentAd.zone_id\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this),\n                        ads.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 mt-1\",\n                            children: [\n                                \"Ad \",\n                                currentAdIndex + 1,\n                                \" of \",\n                                ads.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    top: '2px',\n                    right: '2px',\n                    fontSize: '10px',\n                    color: '#6c757d',\n                    backgroundColor: 'rgba(255,255,255,0.9)',\n                    padding: '1px 4px',\n                    borderRadius: '2px',\n                    zIndex: 10\n                },\n                children: \"إعلان\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this),\n            showDebug && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    position: 'absolute',\n                    bottom: '2px',\n                    left: '2px',\n                    fontSize: '10px',\n                    color: '#007bff',\n                    backgroundColor: 'rgba(255,255,255,0.9)',\n                    padding: '1px 4px',\n                    borderRadius: '2px',\n                    zIndex: 10\n                },\n                children: [\n                    \"ID: \",\n                    currentAd.id.substring(0, 8)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n                lineNumber: 223,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n        lineNumber: 163,\n        columnNumber: 5\n    }, this);\n}\n_s(SupabaseAdManager, \"ONXCpC8rmkckTjqGjyy3x4fGtSw=\");\n_c = SupabaseAdManager;\n// Pre-configured components for easy use\nfunction SupabaseHeaderAd(param) {\n    let { currentPage, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupabaseAdManager, {\n        position: \"header\",\n        currentPage: currentPage,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n        lineNumber: 245,\n        columnNumber: 10\n    }, this);\n}\n_c1 = SupabaseHeaderAd;\nfunction SupabaseSidebarAd(param) {\n    let { currentPage, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupabaseAdManager, {\n        position: \"sidebar\",\n        currentPage: currentPage,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n        lineNumber: 249,\n        columnNumber: 10\n    }, this);\n}\n_c2 = SupabaseSidebarAd;\nfunction SupabaseInContentAd(param) {\n    let { currentPage, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupabaseAdManager, {\n        position: \"in-content\",\n        currentPage: currentPage,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n        lineNumber: 253,\n        columnNumber: 10\n    }, this);\n}\n_c3 = SupabaseInContentAd;\nfunction SupabaseFooterAd(param) {\n    let { currentPage, className } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SupabaseAdManager, {\n        position: \"footer\",\n        currentPage: currentPage,\n        className: className\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SupabaseAdManager.tsx\",\n        lineNumber: 257,\n        columnNumber: 10\n    }, this);\n}\n_c4 = SupabaseFooterAd;\n// Hook for managing ads\nfunction useSupabaseAds(currentPage, position) {\n    _s1();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useSupabaseAds.useEffect\": ()=>{\n            const loadAds = {\n                \"useSupabaseAds.useEffect.loadAds\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const fetchedAds = await (0,_lib_supabase_ads__WEBPACK_IMPORTED_MODULE_2__.getAdsForPage)(currentPage, position);\n                        setAds(fetchedAds);\n                        setError(null);\n                    } catch (err) {\n                        setError(err instanceof Error ? err.message : 'Failed to load ads');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"useSupabaseAds.useEffect.loadAds\"];\n            loadAds();\n        }\n    }[\"useSupabaseAds.useEffect\"], [\n        currentPage,\n        position\n    ]);\n    return {\n        ads,\n        isLoading,\n        error\n    };\n}\n_s1(useSupabaseAds, \"vrH0QBkN/90iyK5Cka4/uzyTdp8=\");\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"SupabaseAdManager\");\n$RefreshReg$(_c1, \"SupabaseHeaderAd\");\n$RefreshReg$(_c2, \"SupabaseSidebarAd\");\n$RefreshReg$(_c3, \"SupabaseInContentAd\");\n$RefreshReg$(_c4, \"SupabaseFooterAd\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/SupabaseAdManager.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/supabase-ads.ts":
/*!*********************************!*\
  !*** ./src/lib/supabase-ads.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CREATE_ADS_TABLE_SQL: () => (/* binding */ CREATE_ADS_TABLE_SQL),\n/* harmony export */   CREATE_SQL_FUNCTIONS: () => (/* binding */ CREATE_SQL_FUNCTIONS),\n/* harmony export */   createAd: () => (/* binding */ createAd),\n/* harmony export */   createCodeInjection: () => (/* binding */ createCodeInjection),\n/* harmony export */   deleteAd: () => (/* binding */ deleteAd),\n/* harmony export */   deleteCodeInjection: () => (/* binding */ deleteCodeInjection),\n/* harmony export */   getAdPerformance: () => (/* binding */ getAdPerformance),\n/* harmony export */   getAds: () => (/* binding */ getAds),\n/* harmony export */   getAdsForPage: () => (/* binding */ getAdsForPage),\n/* harmony export */   getAllCodeInjections: () => (/* binding */ getAllCodeInjections),\n/* harmony export */   getCodeInjections: () => (/* binding */ getCodeInjections),\n/* harmony export */   initializeAdsSystem: () => (/* binding */ initializeAdsSystem),\n/* harmony export */   trackAdPerformance: () => (/* binding */ trackAdPerformance),\n/* harmony export */   updateAd: () => (/* binding */ updateAd),\n/* harmony export */   updateCodeInjection: () => (/* binding */ updateCodeInjection)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n// Supabase Ads Management System\n// إدارة الإعلانات باستخدام قاعدة بيانات Supabase\n\n// Create ads table SQL (run this in Supabase SQL editor)\nconst CREATE_ADS_TABLE_SQL = \"\\n-- Create ads table\\nCREATE TABLE IF NOT EXISTS ads (\\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\\n  name VARCHAR(255) NOT NULL,\\n  type VARCHAR(50) NOT NULL DEFAULT 'monetag',\\n  position VARCHAR(50) NOT NULL,\\n  zone_id VARCHAR(100),\\n  script_code TEXT NOT NULL,\\n  html_code TEXT,\\n  enabled BOOLEAN DEFAULT true,\\n  pages JSONB DEFAULT '[]'::jsonb,\\n  priority INTEGER DEFAULT 1,\\n  delay_seconds INTEGER DEFAULT 0,\\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  click_count INTEGER DEFAULT 0,\\n  view_count INTEGER DEFAULT 0,\\n  revenue DECIMAL(10,2) DEFAULT 0\\n);\\n\\n-- Create ad_performance table\\nCREATE TABLE IF NOT EXISTS ad_performance (\\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\\n  ad_id UUID REFERENCES ads(id) ON DELETE CASCADE,\\n  event_type VARCHAR(20) NOT NULL,\\n  page_url VARCHAR(500) NOT NULL,\\n  user_agent TEXT,\\n  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n  ip_address INET\\n);\\n\\n-- Create indexes\\nCREATE INDEX IF NOT EXISTS idx_ads_enabled ON ads(enabled);\\nCREATE INDEX IF NOT EXISTS idx_ads_position ON ads(position);\\nCREATE INDEX IF NOT EXISTS idx_ads_priority ON ads(priority DESC);\\nCREATE INDEX IF NOT EXISTS idx_ad_performance_ad_id ON ad_performance(ad_id);\\nCREATE INDEX IF NOT EXISTS idx_ad_performance_timestamp ON ad_performance(timestamp);\\n\\n-- Enable RLS\\nALTER TABLE ads ENABLE ROW LEVEL SECURITY;\\nALTER TABLE ad_performance ENABLE ROW LEVEL SECURITY;\\n\\n-- Create policies (allow all for now, customize as needed)\\nCREATE POLICY \\\"Allow all operations on ads\\\" ON ads FOR ALL USING (true);\\nCREATE POLICY \\\"Allow all operations on ad_performance\\\" ON ad_performance FOR ALL USING (true);\\n\\n-- Insert default Monetag ads\\nINSERT INTO ads (name, type, position, zone_id, script_code, enabled, pages, priority) VALUES\\n('Monetag Header Banner', 'monetag', 'header', '9593378', \\n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\\\"vemtoutcheeg.com\\\",9593378,document.createElement(\\\"script\\\"));',\\n true, '[\\\"*\\\"]', 10),\\n('Monetag Sidebar', 'monetag', 'sidebar', '9593331',\\n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\\\"vemtoutcheeg.com\\\",9593331,document.createElement(\\\"script\\\"));',\\n true, '[\\\"/articles\\\", \\\"/ai-tools\\\"]', 8),\\n('Monetag In-Content', 'monetag', 'in-content', '9593378',\\n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\\\"vemtoutcheeg.com\\\",9593378,document.createElement(\\\"script\\\"));',\\n true, '[\\\"/articles\\\", \\\"/ai-tools\\\", \\\"/\\\"]', 5);\\n\";\n// Fetch ads from Supabase\nasync function getAds(position, enabled) {\n    try {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').select('*').order('priority', {\n            ascending: false\n        });\n        if (position) {\n            query = query.eq('position', position);\n        }\n        if (enabled !== undefined) {\n            query = query.eq('enabled', enabled);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching ads:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getAds:', error);\n        return [];\n    }\n}\n// Get ads for specific page and position\nasync function getAdsForPage(currentPage, position) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').select('*').eq('position', position).eq('enabled', true).order('priority', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching ads for page:', error);\n            return [];\n        }\n        // Filter ads based on page patterns\n        const filteredAds = (data || []).filter((ad)=>{\n            const pages = ad.pages || [];\n            // If pages array contains \"*\", show on all pages\n            if (pages.includes('*')) return true;\n            // Check if current page matches any pattern\n            return pages.some((pattern)=>{\n                if (pattern === currentPage) return true;\n                if (pattern.endsWith('*') && currentPage.startsWith(pattern.slice(0, -1))) return true;\n                return false;\n            });\n        });\n        return filteredAds;\n    } catch (error) {\n        console.error('Error in getAdsForPage:', error);\n        return [];\n    }\n}\n// Create new ad\nasync function createAd(ad) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').insert([\n            ad\n        ]).select().single();\n        if (error) {\n            console.error('Error creating ad:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createAd:', error);\n        return null;\n    }\n}\n// Update ad\nasync function updateAd(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating ad:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in updateAd:', error);\n        return null;\n    }\n}\n// Delete ad\nasync function deleteAd(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting ad:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error in deleteAd:', error);\n        return false;\n    }\n}\n// Track ad performance\nasync function trackAdPerformance(adId, eventType, pageUrl, userAgent) {\n    try {\n        console.log(\"Tracking ad performance: \".concat(eventType, \" for ad \").concat(adId, \" on \").concat(pageUrl));\n        // Insert performance record\n        const { error: insertError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ad_performance').insert([\n            {\n                ad_id: adId,\n                event_type: eventType,\n                page_url: pageUrl,\n                user_agent: userAgent || (typeof navigator !== 'undefined' ? navigator.userAgent : 'Unknown')\n            }\n        ]);\n        if (insertError) {\n            console.error('Error tracking ad performance:', insertError);\n            return;\n        }\n        // Update ad counters\n        if (eventType === 'click') {\n            const { error: clickError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('increment_ad_clicks', {\n                ad_id: adId\n            });\n            if (clickError) console.error('Error incrementing clicks:', clickError);\n        } else if (eventType === 'view') {\n            const { error: viewError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc('increment_ad_views', {\n                ad_id: adId\n            });\n            if (viewError) console.error('Error incrementing views:', viewError);\n        }\n        console.log(\"Successfully tracked \".concat(eventType, \" for ad \").concat(adId));\n    } catch (error) {\n        console.error('Error in trackAdPerformance:', error);\n    }\n}\n// Get ad performance stats\nasync function getAdPerformance(adId) {\n    let days = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 7;\n    try {\n        const startDate = new Date();\n        startDate.setDate(startDate.getDate() - days);\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ad_performance').select('*').gte('timestamp', startDate.toISOString()).order('timestamp', {\n            ascending: false\n        });\n        if (adId) {\n            query = query.eq('ad_id', adId);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching ad performance:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getAdPerformance:', error);\n        return [];\n    }\n}\n// SQL functions to create (run in Supabase SQL editor)\nconst CREATE_SQL_FUNCTIONS = \"\\n-- Function to increment ad clicks\\nCREATE OR REPLACE FUNCTION increment_ad_clicks(ad_id UUID)\\nRETURNS void AS $$\\nBEGIN\\n  UPDATE ads SET click_count = click_count + 1 WHERE id = ad_id;\\nEND;\\n$$ LANGUAGE plpgsql;\\n\\n-- Function to increment ad views\\nCREATE OR REPLACE FUNCTION increment_ad_views(ad_id UUID)\\nRETURNS void AS $$\\nBEGIN\\n  UPDATE ads SET view_count = view_count + 1 WHERE id = ad_id;\\nEND;\\n$$ LANGUAGE plpgsql;\\n\\n-- Function to get ad stats\\nCREATE OR REPLACE FUNCTION get_ad_stats(days_back INTEGER DEFAULT 7)\\nRETURNS TABLE(\\n  ad_id UUID,\\n  ad_name VARCHAR,\\n  total_views BIGINT,\\n  total_clicks BIGINT,\\n  ctr DECIMAL\\n) AS $$\\nBEGIN\\n  RETURN QUERY\\n  SELECT \\n    a.id,\\n    a.name,\\n    a.view_count,\\n    a.click_count,\\n    CASE \\n      WHEN a.view_count > 0 THEN (a.click_count::DECIMAL / a.view_count::DECIMAL) * 100\\n      ELSE 0\\n    END as ctr\\n  FROM ads a\\n  WHERE a.enabled = true\\n  ORDER BY a.priority DESC;\\nEND;\\n$$ LANGUAGE plpgsql;\\n\";\n// Initialize ads system\nasync function initializeAdsSystem() {\n    try {\n        // Check if ads table exists\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ads').select('id').limit(1);\n        if (error && error.code === 'PGRST116') {\n            // Table doesn't exist\n            console.log('Ads table not found. Please run the SQL setup in Supabase.');\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error initializing ads system:', error);\n        return false;\n    }\n}\n// ==================== CODE INJECTION FUNCTIONS ====================\n// Get code injections by position and page\nasync function getCodeInjections(position, currentPage) {\n    try {\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('code_injections').select('*').eq('enabled', true).order('priority', {\n            ascending: false\n        });\n        if (position) {\n            query = query.eq('position', position);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching code injections:', error);\n            return [];\n        }\n        if (!currentPage) {\n            return data || [];\n        }\n        // Filter by page patterns\n        const filteredInjections = (data || []).filter((injection)=>{\n            const pages = injection.pages || [];\n            // If pages array contains \"*\", show on all pages\n            if (pages.includes('*')) return true;\n            // Check if current page matches any pattern\n            return pages.some((pattern)=>{\n                if (pattern === currentPage) return true;\n                if (pattern.endsWith('*') && currentPage.startsWith(pattern.slice(0, -1))) return true;\n                return false;\n            });\n        });\n        return filteredInjections;\n    } catch (error) {\n        console.error('Error in getCodeInjections:', error);\n        return [];\n    }\n}\n// Create new code injection\nasync function createCodeInjection(injection) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('code_injections').insert([\n            injection\n        ]).select().single();\n        if (error) {\n            console.error('Error creating code injection:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in createCodeInjection:', error);\n        return null;\n    }\n}\n// Update code injection\nasync function updateCodeInjection(id, updates) {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('code_injections').update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq('id', id).select().single();\n        if (error) {\n            console.error('Error updating code injection:', error);\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Error in updateCodeInjection:', error);\n        return null;\n    }\n}\n// Delete code injection\nasync function deleteCodeInjection(id) {\n    try {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('code_injections').delete().eq('id', id);\n        if (error) {\n            console.error('Error deleting code injection:', error);\n            return false;\n        }\n        return true;\n    } catch (error) {\n        console.error('Error in deleteCodeInjection:', error);\n        return false;\n    }\n}\n// Get all code injections for admin\nasync function getAllCodeInjections() {\n    try {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('code_injections').select('*').order('priority', {\n            ascending: false\n        });\n        if (error) {\n            console.error('Error fetching all code injections:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Error in getAllCodeInjections:', error);\n        return [];\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase-ads.ts\n"));

/***/ })

});