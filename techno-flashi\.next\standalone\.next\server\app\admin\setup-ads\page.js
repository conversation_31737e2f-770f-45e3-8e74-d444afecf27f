(()=>{var e={};e.id=1191,e.ids=[1191],e.modules={2838:(e,s,r)=>{Promise.resolve().then(r.bind(r,76799))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16433:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\setup-ads\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\setup-ads\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,s,r)=>{"use strict";r.d(s,{ProtectedRoute:()=>a});var t=r(60687),l=r(63213),i=r(16189);function a({children:e}){let{user:s,loading:r}=(0,l.A)();return((0,i.useRouter)(),r)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,t.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):s?(0,t.jsx)(t.Fragment,{children:e}):null}r(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,s,r)=>{Promise.resolve().then(r.bind(r,67083))},39790:(e,s,r)=>{Promise.resolve().then(r.bind(r,16433))},39947:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=r(65239),l=r(48088),i=r(88170),a=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["admin",{children:["setup-ads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16433)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\setup-ads\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\setup-ads\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/admin/setup-ads/page",pathname:"/admin/setup-ads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,s,r)=>{"use strict";r.d(s,{ProtectedRoute:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},76799:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(60687),l=r(43210),i=r(15720);function a(){let[e,s]=(0,l.useState)(1),[r,a]=(0,l.useState)({}),n=async(e,s)=>{try{await navigator.clipboard.writeText(e),a({...r,[s]:!0}),setTimeout(()=>{a({...r,[s]:!1})},2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83D\uDE80 إعداد نظام الإعلانات"}),(0,t.jsx)("p",{className:"text-gray-600",children:"إعداد قاعدة بيانات Supabase لإدارة الإعلانات"})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"خطوات الإعداد"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["الخطوة ",e," من 4"]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:[1,2,3,4].map(s=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${e>=s?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}`,children:s}),s<4&&(0,t.jsx)("div",{className:`w-12 h-1 ${e>s?"bg-blue-600":"bg-gray-200"}`})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[1===e&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCCB الخطوة 1: فتح Supabase Dashboard"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"التعليمات:"}),(0,t.jsxs)("ol",{className:"list-decimal list-inside text-blue-800 space-y-2",children:[(0,t.jsxs)("li",{children:["اذهب إلى ",(0,t.jsx)("a",{href:"https://supabase.com/dashboard",target:"_blank",className:"underline",children:"Supabase Dashboard"})]}),(0,t.jsxs)("li",{children:["اختر مشروعك: ",(0,t.jsx)("strong",{children:"tflash.dev"})]}),(0,t.jsxs)("li",{children:["اذهب إلى ",(0,t.jsx)("strong",{children:"SQL Editor"})," من القائمة الجانبية"]}),(0,t.jsxs)("li",{children:["اضغط ",(0,t.jsx)("strong",{children:"New Query"})]})]})]}),(0,t.jsx)("button",{onClick:()=>s(2),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: إنشاء الجداول ➡️"})]})]}),2===e&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDDC4️ الخطوة 2: إنشاء جداول قاعدة البيانات"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"انسخ والصق الكود التالي في SQL Editor:"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded font-mono text-sm overflow-x-auto max-h-96",children:(0,t.jsx)("pre",{children:i.yo})}),(0,t.jsx)("button",{onClick:()=>n(i.yo,"tables"),className:"absolute top-2 right-2 bg-gray-700 text-white px-3 py-1 rounded text-xs hover:bg-gray-600",children:r.tables?"✅ تم النسخ":"\uD83D\uDCCB نسخ"})]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,t.jsx)("strong",{children:"مهم:"})," اضغط ",(0,t.jsx)("strong",{children:"Run"})," في Supabase بعد لصق الكود"]})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:()=>s(1),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,t.jsx)("button",{onClick:()=>s(3),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: إنشاء الدوال ➡️"})]})]})]}),3===e&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"⚙️ الخطوة 3: إنشاء الدوال المساعدة"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"انسخ والصق الكود التالي في SQL Editor (استعلام جديد):"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded font-mono text-sm overflow-x-auto max-h-96",children:(0,t.jsx)("pre",{children:i.AF})}),(0,t.jsx)("button",{onClick:()=>n(i.AF,"functions"),className:"absolute top-2 right-2 bg-gray-700 text-white px-3 py-1 rounded text-xs hover:bg-gray-600",children:r.functions?"✅ تم النسخ":"\uD83D\uDCCB نسخ"})]}),(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,t.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,t.jsx)("strong",{children:"مهم:"})," اضغط ",(0,t.jsx)("strong",{children:"Run"})," في Supabase بعد لصق الكود"]})}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:()=>s(2),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,t.jsx)("button",{onClick:()=>s(4),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: الانتهاء ➡️"})]})]})]}),4===e&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83C\uDF89 الخطوة 4: تم الإعداد بنجاح!"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"✅ تم إنشاء النظام بنجاح"}),(0,t.jsx)("p",{className:"text-green-800 text-sm",children:"تم إعداد قاعدة البيانات وإنشاء الجداول والدوال المطلوبة."})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCCA ما تم إنشاؤه:"}),(0,t.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,t.jsxs)("li",{children:["\uD83D\uDDC4️ جدول ",(0,t.jsx)("code",{children:"ads"})," - لحفظ الإعلانات"]}),(0,t.jsxs)("li",{children:["\uD83D\uDCC8 جدول ",(0,t.jsx)("code",{children:"ad_performance"})," - لتتبع الأداء"]}),(0,t.jsx)("li",{children:"⚙️ دوال مساعدة لإدارة الإحصائيات"}),(0,t.jsx)("li",{children:"\uD83D\uDD12 سياسات الأمان (RLS)"}),(0,t.jsx)("li",{children:"\uD83D\uDCCB 3 إعلانات Monetag افتراضية"})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-yellow-900 mb-2",children:"\uD83D\uDE80 الخطوات التالية:"}),(0,t.jsxs)("ol",{className:"text-yellow-800 text-sm space-y-1 list-decimal list-inside",children:[(0,t.jsxs)("li",{children:["اذهب إلى ",(0,t.jsx)("strong",{children:"إدارة الإعلانات"})," لإضافة إعلانات جديدة"]}),(0,t.jsx)("li",{children:"تحقق من ظهور الإعلانات في الصفحة الرئيسية"}),(0,t.jsx)("li",{children:"راقب الأداء والإحصائيات"})]})]}),(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("button",{onClick:()=>s(3),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,t.jsx)("a",{href:"/admin/supabase-ads",className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 inline-block text-center",children:"\uD83C\uDFAF إدارة الإعلانات"}),(0,t.jsx)("a",{href:"/",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 inline-block text-center",children:"\uD83C\uDFE0 الصفحة الرئيسية"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mt-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"❓ تحتاج مساعدة؟"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"\uD83D\uDD17 روابط مفيدة"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"https://supabase.com/dashboard",target:"_blank",className:"text-blue-600 hover:underline",children:"Supabase Dashboard"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/admin/supabase-ads",className:"text-blue-600 hover:underline",children:"إدارة الإعلانات"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"/",className:"text-blue-600 hover:underline",children:"الصفحة الرئيسية"})})]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"\uD83D\uDEE0️ استكشاف الأخطاء"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,t.jsx)("li",{children:"• تأكد من تشغيل جميع الاستعلامات"}),(0,t.jsx)("li",{children:"• تحقق من عدم وجود أخطاء في Console"}),(0,t.jsx)("li",{children:"• أعد تحميل الصفحة بعد الإعداد"})]})]})]})]})]})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97579:(e,s,r)=>{Promise.resolve().then(r.bind(r,20769))},99111:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(37413),l=r(67083);function i({children:e}){return(0,t.jsx)(l.ProtectedRoute,{children:(0,t.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}r(31240)}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,6437,2675,3595],()=>r(39947));module.exports=t})();