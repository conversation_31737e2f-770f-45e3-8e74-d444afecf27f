(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1191],{15714:(e,s,l)=>{Promise.resolve().then(l.bind(l,27317))},27317:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>t});var a=l(95155),r=l(12115),d=l(4332);function t(){let[e,s]=(0,r.useState)(1),[l,t]=(0,r.useState)({}),i=async(e,s)=>{try{await navigator.clipboard.writeText(e),t({...l,[s]:!0}),setTimeout(()=>{t({...l,[s]:!1})},2e3)}catch(e){console.error("Failed to copy text: ",e)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-6",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83D\uDE80 إعداد نظام الإعلانات"}),(0,a.jsx)("p",{className:"text-gray-600",children:"إعداد قاعدة بيانات Supabase لإدارة الإعلانات"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:"خطوات الإعداد"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["الخطوة ",e," من 4"]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:[1,2,3,4].map(s=>(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat(e>=s?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"),children:s}),s<4&&(0,a.jsx)("div",{className:"w-12 h-1 ".concat(e>s?"bg-blue-600":"bg-gray-200")})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[1===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDCCB الخطوة 1: فتح Supabase Dashboard"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"التعليمات:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside text-blue-800 space-y-2",children:[(0,a.jsxs)("li",{children:["اذهب إلى ",(0,a.jsx)("a",{href:"https://supabase.com/dashboard",target:"_blank",className:"underline",children:"Supabase Dashboard"})]}),(0,a.jsxs)("li",{children:["اختر مشروعك: ",(0,a.jsx)("strong",{children:"tflash.dev"})]}),(0,a.jsxs)("li",{children:["اذهب إلى ",(0,a.jsx)("strong",{children:"SQL Editor"})," من القائمة الجانبية"]}),(0,a.jsxs)("li",{children:["اضغط ",(0,a.jsx)("strong",{children:"New Query"})]})]})]}),(0,a.jsx)("button",{onClick:()=>s(2),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: إنشاء الجداول ➡️"})]})]}),2===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83D\uDDC4️ الخطوة 2: إنشاء جداول قاعدة البيانات"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"انسخ والصق الكود التالي في SQL Editor:"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded font-mono text-sm overflow-x-auto max-h-96",children:(0,a.jsx)("pre",{children:d.yo})}),(0,a.jsx)("button",{onClick:()=>i(d.yo,"tables"),className:"absolute top-2 right-2 bg-gray-700 text-white px-3 py-1 rounded text-xs hover:bg-gray-600",children:l.tables?"✅ تم النسخ":"\uD83D\uDCCB نسخ"})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,a.jsx)("strong",{children:"مهم:"})," اضغط ",(0,a.jsx)("strong",{children:"Run"})," في Supabase بعد لصق الكود"]})}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:()=>s(1),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,a.jsx)("button",{onClick:()=>s(3),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: إنشاء الدوال ➡️"})]})]})]}),3===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"⚙️ الخطوة 3: إنشاء الدوال المساعدة"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600",children:"انسخ والصق الكود التالي في SQL Editor (استعلام جديد):"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"bg-gray-900 text-green-400 p-4 rounded font-mono text-sm overflow-x-auto max-h-96",children:(0,a.jsx)("pre",{children:d.AF})}),(0,a.jsx)("button",{onClick:()=>i(d.AF,"functions"),className:"absolute top-2 right-2 bg-gray-700 text-white px-3 py-1 rounded text-xs hover:bg-gray-600",children:l.functions?"✅ تم النسخ":"\uD83D\uDCCB نسخ"})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:(0,a.jsxs)("p",{className:"text-yellow-800 text-sm",children:[(0,a.jsx)("strong",{children:"مهم:"})," اضغط ",(0,a.jsx)("strong",{children:"Run"})," في Supabase بعد لصق الكود"]})}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:()=>s(2),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,a.jsx)("button",{onClick:()=>s(4),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"التالي: الانتهاء ➡️"})]})]})]}),4===e&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"\uD83C\uDF89 الخطوة 4: تم الإعداد بنجاح!"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-green-900 mb-2",children:"✅ تم إنشاء النظام بنجاح"}),(0,a.jsx)("p",{className:"text-green-800 text-sm",children:"تم إعداد قاعدة البيانات وإنشاء الجداول والدوال المطلوبة."})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"\uD83D\uDCCA ما تم إنشاؤه:"}),(0,a.jsxs)("ul",{className:"text-blue-800 text-sm space-y-1",children:[(0,a.jsxs)("li",{children:["\uD83D\uDDC4️ جدول ",(0,a.jsx)("code",{children:"ads"})," - لحفظ الإعلانات"]}),(0,a.jsxs)("li",{children:["\uD83D\uDCC8 جدول ",(0,a.jsx)("code",{children:"ad_performance"})," - لتتبع الأداء"]}),(0,a.jsx)("li",{children:"⚙️ دوال مساعدة لإدارة الإحصائيات"}),(0,a.jsx)("li",{children:"\uD83D\uDD12 سياسات الأمان (RLS)"}),(0,a.jsx)("li",{children:"\uD83D\uDCCB 3 إعلانات Monetag افتراضية"})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-yellow-900 mb-2",children:"\uD83D\uDE80 الخطوات التالية:"}),(0,a.jsxs)("ol",{className:"text-yellow-800 text-sm space-y-1 list-decimal list-inside",children:[(0,a.jsxs)("li",{children:["اذهب إلى ",(0,a.jsx)("strong",{children:"إدارة الإعلانات"})," لإضافة إعلانات جديدة"]}),(0,a.jsx)("li",{children:"تحقق من ظهور الإعلانات في الصفحة الرئيسية"}),(0,a.jsx)("li",{children:"راقب الأداء والإحصائيات"})]})]}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("button",{onClick:()=>s(3),className:"bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600",children:"⬅️ السابق"}),(0,a.jsx)("a",{href:"/admin/supabase-ads",className:"bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 inline-block text-center",children:"\uD83C\uDFAF إدارة الإعلانات"}),(0,a.jsx)("a",{href:"/",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 inline-block text-center",children:"\uD83C\uDFE0 الصفحة الرئيسية"})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"❓ تحتاج مساعدة؟"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"\uD83D\uDD17 روابط مفيدة"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"https://supabase.com/dashboard",target:"_blank",className:"text-blue-600 hover:underline",children:"Supabase Dashboard"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/admin/supabase-ads",className:"text-blue-600 hover:underline",children:"إدارة الإعلانات"})}),(0,a.jsx)("li",{children:(0,a.jsx)("a",{href:"/",className:"text-blue-600 hover:underline",children:"الصفحة الرئيسية"})})]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"\uD83D\uDEE0️ استكشاف الأخطاء"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• تأكد من تشغيل جميع الاستعلامات"}),(0,a.jsx)("li",{children:"• تحقق من عدم وجود أخطاء في Console"}),(0,a.jsx)("li",{children:"• أعد تحميل الصفحة بعد الإعداد"})]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>s(15714)),_N_E=e.O()}]);