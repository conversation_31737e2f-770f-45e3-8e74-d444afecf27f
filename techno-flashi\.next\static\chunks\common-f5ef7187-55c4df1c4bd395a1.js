"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[680],{4332:(e,n,r)=>{r.d(n,{AF:()=>l,DO:()=>s,Q$:()=>N,Re:()=>E,Yv:()=>c,dX:()=>i,hI:()=>d,jj:()=>T,ky:()=>A,nB:()=>_,np:()=>a,oX:()=>p,u1:()=>u,yo:()=>o});var t=r(42099);let o="\n-- Create ads table\nCREATE TABLE IF NOT EXISTS ads (\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n  name VARCHAR(255) NOT NULL,\n  type VARCHAR(50) NOT NULL DEFAULT 'monetag',\n  position VARCHAR(50) NOT NULL,\n  zone_id VARCHAR(100),\n  script_code TEXT NOT NULL,\n  html_code TEXT,\n  enabled BOOLEAN DEFAULT true,\n  pages JSONB DEFAULT '[]'::jsonb,\n  priority INTEGER DEFAULT 1,\n  delay_seconds INTEGER DEFAULT 0,\n  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  click_count INTEGER DEFAULT 0,\n  view_count INTEGER DEFAULT 0,\n  revenue DECIMAL(10,2) DEFAULT 0\n);\n\n-- Create ad_performance table\nCREATE TABLE IF NOT EXISTS ad_performance (\n  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,\n  ad_id UUID REFERENCES ads(id) ON DELETE CASCADE,\n  event_type VARCHAR(20) NOT NULL,\n  page_url VARCHAR(500) NOT NULL,\n  user_agent TEXT,\n  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n  ip_address INET\n);\n\n-- Create indexes\nCREATE INDEX IF NOT EXISTS idx_ads_enabled ON ads(enabled);\nCREATE INDEX IF NOT EXISTS idx_ads_position ON ads(position);\nCREATE INDEX IF NOT EXISTS idx_ads_priority ON ads(priority DESC);\nCREATE INDEX IF NOT EXISTS idx_ad_performance_ad_id ON ad_performance(ad_id);\nCREATE INDEX IF NOT EXISTS idx_ad_performance_timestamp ON ad_performance(timestamp);\n\n-- Enable RLS\nALTER TABLE ads ENABLE ROW LEVEL SECURITY;\nALTER TABLE ad_performance ENABLE ROW LEVEL SECURITY;\n\n-- Create policies (allow all for now, customize as needed)\nCREATE POLICY \"Allow all operations on ads\" ON ads FOR ALL USING (true);\nCREATE POLICY \"Allow all operations on ad_performance\" ON ad_performance FOR ALL USING (true);\n\n-- Insert default Monetag ads\nINSERT INTO ads (name, type, position, zone_id, script_code, enabled, pages, priority) VALUES\n('Monetag Header Banner', 'monetag', 'header', '9593378', \n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\"vemtoutcheeg.com\",9593378,document.createElement(\"script\"));',\n true, '[\"*\"]', 10),\n('Monetag Sidebar', 'monetag', 'sidebar', '9593331',\n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\"vemtoutcheeg.com\",9593331,document.createElement(\"script\"));',\n true, '[\"/articles\", \"/ai-tools\"]', 8),\n('Monetag In-Content', 'monetag', 'in-content', '9593378',\n '(function(d,z,s){s.src=''https://''+d+''/400/''+z;try{(document.body||document.documentElement).appendChild(s)}catch(e){}})(\"vemtoutcheeg.com\",9593378,document.createElement(\"script\"));',\n true, '[\"/articles\", \"/ai-tools\", \"/\"]', 5);\n";async function i(e,n){try{let r=t.ND.from("ads").select("*").order("priority",{ascending:!1});e&&(r=r.eq("position",e)),void 0!==n&&(r=r.eq("enabled",n));let{data:o,error:i}=await r;if(i)return console.error("Error fetching ads:",i),[];return o||[]}catch(e){return console.error("Error in getAds:",e),[]}}async function a(e,n){try{let{data:r,error:o}=await t.ND.from("ads").select("*").eq("position",n).eq("enabled",!0).order("priority",{ascending:!1});if(o)return console.error("Error fetching ads for page:",o),[];return(r||[]).filter(n=>{let r=n.pages||[];return!!r.includes("*")||r.some(n=>!!(n===e||n.endsWith("*")&&e.startsWith(n.slice(0,-1))))})}catch(e){return console.error("Error in getAdsForPage:",e),[]}}async function c(e){try{let{data:n,error:r}=await t.ND.from("ads").insert([e]).select().single();if(r)return console.error("Error creating ad:",r),null;return n}catch(e){return console.error("Error in createAd:",e),null}}async function d(e,n){try{let{data:r,error:o}=await t.ND.from("ads").update({...n,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(o)return console.error("Error updating ad:",o),null;return r}catch(e){return console.error("Error in updateAd:",e),null}}async function s(e){try{let{error:n}=await t.ND.from("ads").delete().eq("id",e);if(n)return console.error("Error deleting ad:",n),!1;return!0}catch(e){return console.error("Error in deleteAd:",e),!1}}async function E(e,n,r,o){try{let{error:i}=await t.ND.from("ad_performance").insert([{ad_id:e,event_type:n,page_url:r,user_agent:o||("undefined"!=typeof navigator?navigator.userAgent:"Unknown")}]);if(i)return void console.error("Error tracking ad performance:",i);if("click"===n){let{error:n}=await t.ND.rpc("increment_ad_clicks",{ad_id:e});n&&console.error("Error incrementing clicks:",n)}else if("view"===n){let{error:n}=await t.ND.rpc("increment_ad_views",{ad_id:e});n&&console.error("Error incrementing views:",n)}}catch(e){console.error("Error in trackAdPerformance:",e)}}let l="\n-- Function to increment ad clicks\nCREATE OR REPLACE FUNCTION increment_ad_clicks(ad_id UUID)\nRETURNS void AS $$\nBEGIN\n  UPDATE ads SET click_count = click_count + 1 WHERE id = ad_id;\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Function to increment ad views\nCREATE OR REPLACE FUNCTION increment_ad_views(ad_id UUID)\nRETURNS void AS $$\nBEGIN\n  UPDATE ads SET view_count = view_count + 1 WHERE id = ad_id;\nEND;\n$$ LANGUAGE plpgsql;\n\n-- Function to get ad stats\nCREATE OR REPLACE FUNCTION get_ad_stats(days_back INTEGER DEFAULT 7)\nRETURNS TABLE(\n  ad_id UUID,\n  ad_name VARCHAR,\n  total_views BIGINT,\n  total_clicks BIGINT,\n  ctr DECIMAL\n) AS $$\nBEGIN\n  RETURN QUERY\n  SELECT \n    a.id,\n    a.name,\n    a.view_count,\n    a.click_count,\n    CASE \n      WHEN a.view_count > 0 THEN (a.click_count::DECIMAL / a.view_count::DECIMAL) * 100\n      ELSE 0\n    END as ctr\n  FROM ads a\n  WHERE a.enabled = true\n  ORDER BY a.priority DESC;\nEND;\n$$ LANGUAGE plpgsql;\n";async function u(){try{let{data:e,error:n}=await t.ND.from("ads").select("id").limit(1);if(n&&"PGRST116"===n.code)return!1;return!0}catch(e){return console.error("Error initializing ads system:",e),!1}}async function N(e,n){try{let r=t.ND.from("code_injections").select("*").eq("enabled",!0).order("priority",{ascending:!1});e&&(r=r.eq("position",e));let{data:o,error:i}=await r;if(i)return console.error("Error fetching code injections:",i),[];if(!n)return o||[];return(o||[]).filter(e=>{let r=e.pages||[];return!!r.includes("*")||r.some(e=>!!(e===n||e.endsWith("*")&&n.startsWith(e.slice(0,-1))))})}catch(e){return console.error("Error in getCodeInjections:",e),[]}}async function T(e){try{let{data:n,error:r}=await t.ND.from("code_injections").insert([e]).select().single();if(r)return console.error("Error creating code injection:",r),null;return n}catch(e){return console.error("Error in createCodeInjection:",e),null}}async function A(e,n){try{let{data:r,error:o}=await t.ND.from("code_injections").update({...n,updated_at:new Date().toISOString()}).eq("id",e).select().single();if(o)return console.error("Error updating code injection:",o),null;return r}catch(e){return console.error("Error in updateCodeInjection:",e),null}}async function p(e){try{let{error:n}=await t.ND.from("code_injections").delete().eq("id",e);if(n)return console.error("Error deleting code injection:",n),!1;return!0}catch(e){return console.error("Error in deleteCodeInjection:",e),!1}}async function _(){try{let{data:e,error:n}=await t.ND.from("code_injections").select("*").order("priority",{ascending:!1});if(n)return console.error("Error fetching all code injections:",n),[];return e||[]}catch(e){return console.error("Error in getAllCodeInjections:",e),[]}}},42099:(e,n,r)=>{r.d(n,{ND:()=>c});var t=r(76572),o=r(49509);let i=o.env.NEXT_PUBLIC_SUPABASE_URL||"https://zgktrwpladrkhhemhnni.supabase.co",a=o.env.NEXT_PUBLIC_SUPABASE_ANON_KEY||"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04";if(!i)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let c=(0,t.UU)(i,a,{db:{schema:"public"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})},70861:(e,n,r)=>{function t(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.trim().toLowerCase())}r.d(n,{DT:()=>t}),r(42099)},71560:(e,n,r)=>{function t(e){var n;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",t=Date.now(),o=(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"id";return"undefined"!=typeof crypto&&crypto.randomUUID?"".concat(e,"-").concat(crypto.randomUUID().substring(0,8)):"".concat(e,"-").concat(Date.now(),"-").concat(Math.random().toString(36).substring(2,8))})("file").split("-")[1],i=(null==(n=e.split(".").pop())?void 0:n.toLowerCase())||"jpg",a="".concat(t,"-").concat(o,".").concat(i);return r?"".concat(r,"/").concat(a):a}function o(){return new Date().toISOString()}function i(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return Math.max(1,Math.ceil(e.trim().split(/\s+/).length/n))}r.d(n,{_C:()=>i,i0:()=>o,nw:()=>t})}}]);