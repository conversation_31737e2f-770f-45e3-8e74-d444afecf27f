"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"69b8bc0d0ecc\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGlzbWFpbFxcRG93bmxvYWRzXFxOZXcgZm9sZGVyICg0KVxcdGVjaG5vLWZsYXNoaVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjliOGJjMGQwZWNjXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProfessionalHeader.tsx":
/*!***********************************************!*\
  !*** ./src/components/ProfessionalHeader.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfessionalHeader: () => (/* binding */ ProfessionalHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_InteractiveEffects__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InteractiveEffects */ \"(app-pages-browser)/./src/components/InteractiveEffects.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProfessionalHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProfessionalHeader() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { user, signOut, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProfessionalHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProfessionalHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 20);\n                }\n            }[\"ProfessionalHeader.useEffect.handleScroll\"];\n            const handleResize = {\n                \"ProfessionalHeader.useEffect.handleResize\": ()=>{\n                    if (window.innerWidth >= 1024) {\n                        setIsMenuOpen(false);\n                    }\n                }\n            }[\"ProfessionalHeader.useEffect.handleResize\"];\n            window.addEventListener('scroll', handleScroll);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"ProfessionalHeader.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"ProfessionalHeader.useEffect\"];\n        }\n    }[\"ProfessionalHeader.useEffect\"], []);\n    // إغلاق القائمة عند الضغط على Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProfessionalHeader.useEffect\": ()=>{\n            const handleEscape = {\n                \"ProfessionalHeader.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape') {\n                        setIsMenuOpen(false);\n                    }\n                }\n            }[\"ProfessionalHeader.useEffect.handleEscape\"];\n            if (isMenuOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"ProfessionalHeader.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"ProfessionalHeader.useEffect\"];\n        }\n    }[\"ProfessionalHeader.useEffect\"], [\n        isMenuOpen\n    ]);\n    const navigationItems = [\n        {\n            href: '/',\n            label: 'الرئيسية',\n            icon: '🏠'\n        },\n        {\n            href: '/articles',\n            label: 'المقالات',\n            icon: '📰'\n        },\n        {\n            href: '/ai-tools',\n            label: 'أدوات الذكاء الاصطناعي',\n            icon: '🤖'\n        },\n        {\n            href: '/services',\n            label: 'الخدمات',\n            icon: '⚙️'\n        },\n        {\n            href: '/about',\n            label: 'من نحن',\n            icon: '👥'\n        },\n        {\n            href: '/contact',\n            label: 'اتصل بنا',\n            icon: '📞'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header header-interactive gradient-bg-interactive gpu-accelerated \".concat(isScrolled ? 'scrolled' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InteractiveEffects__WEBPACK_IMPORTED_MODULE_4__.InteractiveEffects, {\n                        target: \"header\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"logo-icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"ت\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"logo-text\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"logo-title\",\n                                                children: \"تكنو فلاش\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"logo-subtitle\",\n                                                children: \"أحدث أخبار التكنولوجيا\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"desktop-nav\",\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: \"nav-link header-link gpu-accelerated\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            item.label\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InteractiveEffects__WEBPACK_IMPORTED_MODULE_4__.ButtonHoverEffect, {\n                                        variant: \"secondary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"btn btn-ghost btn-sm user-menu interactive-shadow gpu-accelerated\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"hidden lg:inline\",\n                                                    children: \"بحث\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"user-menu\",\n                                        children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/admin\",\n                                                    className: \"btn btn-outline btn-sm\",\n                                                    children: \"لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"btn btn-ghost btn-sm text-error\",\n                                                    children: \"تسجيل الخروج\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 19\n                                        }, this) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: \"mobile-menu-toggle \".concat(isMenuOpen ? 'open' : ''),\n                                        \"aria-label\": \"فتح القائمة\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mobile-menu-icon\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 133,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-menu-overlay lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mobile-menu-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-menu-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"mobile-nav\",\n                                children: [\n                                    navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            className: \"mobile-nav-link\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-nav-icon\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.label\n                                            ]\n                                        }, item.href, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-user-menu\",\n                                        children: !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/admin\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        className: \"mobile-nav-link\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mobile-nav-icon\",\n                                                                children: \"⚙️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"لوحة التحكم\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            signOut();\n                                                            setIsMenuOpen(false);\n                                                        },\n                                                        className: \"mobile-nav-link text-error\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mobile-nav-icon\",\n                                                                children: \"\\uD83D\\uDEAA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"تسجيل الخروج\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 25\n                                            }, this) : null\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 lg:h-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProfessionalHeader, \"aDNwtio0VWcrNxdRNzEfEOczo4U=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProfessionalHeader;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProfessionalHeader.tsx\n"));

/***/ })

});