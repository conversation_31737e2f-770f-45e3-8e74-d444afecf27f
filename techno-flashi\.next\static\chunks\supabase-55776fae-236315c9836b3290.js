"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8888],{44761:(e,t,s)=>{s.d(t,{UJ:()=>ep});let r="2.70.0",i={"X-Client-Info":`gotrue-js/${r}`},a="X-Supabase-Api-Version",n={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},o=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class l extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function u(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class h extends l{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class c extends l{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class d extends l{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class f extends d{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class g extends d{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class w extends d{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class y extends d{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class p extends d{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class _ extends d{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function b(e){return u(e)&&"AuthRetryableFetchError"===e.name}class v extends d{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class m extends d{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let k="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),S=" 	\n\r=".split(""),T=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<S.length;t+=1)e[S[t].charCodeAt(0)]=-2;for(let t=0;t<k.length;t+=1)e[k[t].charCodeAt(0)]=t;return e})();function I(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)s(k[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)s(k[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function A(e,t,s){let r=T[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===r)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function E(e){let t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},a=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return s(e);for(let s=1;s<6;s+=1)if((e>>7-s&1)==0){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let t=0;t<e.length;t+=1)A(e.charCodeAt(t),i,a);return t.join("")}let x=()=>"undefined"!=typeof window&&"undefined"!=typeof document,j={tested:!1,writable:!1},O=()=>{if(!x())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(j.tested)return j.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),j.tested=!0,j.writable=!0}catch(e){j.tested=!0,j.writable=!1}return j.writable},R=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},C=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,P=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},$=async(e,t)=>{let s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(e){return s}},q=async(e,t)=>{await e.removeItem(t)};class U{constructor(){this.promise=new U.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function L(e){let t=e.split(".");if(3!==t.length)throw new m("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!o.test(t[e]))throw new m("JWT not in base64url format");return{header:JSON.parse(E(t[0])),payload:JSON.parse(E(t[1])),signature:function(e){let t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)A(e.charCodeAt(t),s,r);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function D(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function N(e){return("0"+e.toString(16)).substr(-2)}async function W(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function M(e){return"undefined"==typeof crypto||void 0===crypto.subtle||"undefined"==typeof TextEncoder?(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e):btoa(await W(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function B(e,t,s=!1){let r=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,N).join("")}(),i=r;s&&(i+="/PASSWORD_RECOVERY"),await P(e,`${t}-code-verifier`,i);let a=await M(r),n=r===a?"plain":"s256";return[a,n]}U.promiseConstructor=Promise;let F=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,z=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function K(e){if(!z.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var G=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};let J=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),V=[502,503,504];async function H(e){var t;let s,r;if(!C(e))throw new _(J(e),0);if(V.includes(e.status))throw new _(J(e),e.status);try{s=await e.json()}catch(e){throw new c(J(e),e)}let i=function(e){let t=e.headers.get(a);if(!t||!t.match(F))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(i&&i.getTime()>=n["2024-01-01"].timestamp&&"object"==typeof s&&s&&"string"==typeof s.code?r=s.code:"object"==typeof s&&s&&"string"==typeof s.error_code&&(r=s.error_code),r){if("weak_password"===r)throw new v(J(s),e.status,(null==(t=s.weak_password)?void 0:t.reasons)||[]);else if("session_not_found"===r)throw new f}else if("object"==typeof s&&s&&"object"==typeof s.weak_password&&s.weak_password&&Array.isArray(s.weak_password.reasons)&&s.weak_password.reasons.length&&s.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new v(J(s),e.status,s.weak_password.reasons);throw new h(J(s),e.status||500,r)}let Y=(e,t,s,r)=>{let i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),s))};async function X(e,t,s,r){var i;let o=Object.assign({},null==r?void 0:r.headers);o[a]||(o[a]=n["2024-01-01"].name),(null==r?void 0:r.jwt)&&(o.Authorization=`Bearer ${r.jwt}`);let l=null!=(i=null==r?void 0:r.query)?i:{};(null==r?void 0:r.redirectTo)&&(l.redirect_to=r.redirectTo);let u=Object.keys(l).length?"?"+new URLSearchParams(l).toString():"",h=await Z(e,t,s+u,{headers:o,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(h):{data:Object.assign({},h),error:null}}async function Z(e,t,s,r,i,a){let n,o=Y(t,r,i,a);try{n=await e(s,Object.assign({},o))}catch(e){throw console.error(e),new _(J(e),0)}if(n.ok||await H(n),null==r?void 0:r.noResolveJson)return n;try{return await n.json()}catch(e){await H(e)}}function Q(e){var t,s,r;let i=null;(r=e).access_token&&r.refresh_token&&r.expires_in&&(i=Object.assign({},e),e.expires_at||(i.expires_at=(s=e.expires_in,Math.round(Date.now()/1e3)+s)));return{data:{session:i,user:null!=(t=e.user)?t:e},error:null}}function ee(e){let t=Q(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function et(e){var t;return{data:{user:null!=(t=e.user)?t:e},error:null}}function es(e){return{data:e,error:null}}function er(e){let{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:a}=e;return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:a},user:Object.assign({},G(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function ei(e){return e}let ea=["global","local","others"];var en=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)0>t.indexOf(r[i])&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]]);return s};class eo{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=R(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=ea[0]){if(0>ea.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${ea.join(", ")}`);try{return await X(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(u(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await X(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:et})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,s=en(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null==s?void 0:s.newEmail,delete r.newEmail),await X(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:er,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(u(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await X(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:et})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,s,r,i,a,n,o;try{let l={nextPage:null,lastPage:0,total:0},u=await X(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!=(s=null==(t=null==e?void 0:e.page)?void 0:t.toString())?s:"",per_page:null!=(i=null==(r=null==e?void 0:e.perPage)?void 0:r.toString())?i:""},xform:ei});if(u.error)throw u.error;let h=await u.json(),c=null!=(a=u.headers.get("x-total-count"))?a:0,d=null!=(o=null==(n=u.headers.get("link"))?void 0:n.split(","))?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t}),l.total=parseInt(c)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(e){if(u(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){K(e);try{return await X(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:et})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){K(e);try{return await X(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:et})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){K(e);try{return await X(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:et})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){K(e.userId);try{let{data:t,error:s}=await X(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(e){if(u(e))return{data:null,error:e};throw e}}async _deleteFactor(e){K(e.userId),K(e.id);try{return{data:await X(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(u(e))return{data:null,error:e};throw e}}}let el={getItem:e=>O()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{O()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{O()&&globalThis.localStorage.removeItem(e)}};function eu(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}let eh={debug:!!(globalThis&&O()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class ec extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class ed extends ec{}async function ef(e,t,s){eh.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),eh.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(r){eh.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await s()}finally{eh.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}if(0===t)throw eh.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new ed(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(eh.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}))}if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}let eg={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:i,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function ew(e,t,s){return await s()}class ey{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=ey.nextInstanceID,ey.nextInstanceID+=1,this.instanceID>0&&x()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let r=Object.assign(Object.assign({},eg),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new eo({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=R(r.fetch),this.lock=r.lock||ew,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:x()&&(null==(t=null==globalThis?void 0:globalThis.navigator)?void 0:t.locks)?this.lock=ef:this.lock=ew,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:O()?this.storage=el:(this.memoryStorage={},this.storage=eu(this.memoryStorage)):(this.memoryStorage={},this.storage=eu(this.memoryStorage)),x()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null==(s=this.broadcastChannel)||s.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${r}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(e){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}(window.location.href),s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),x()&&this.detectSessionInUrl&&"none"!==s){let{data:r,error:i}=await this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),u(i)&&"AuthImplicitGrantRedirectError"===i.name){let t=null==(e=i.details)?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}let{session:a,redirectType:n}=r;return this._debug("#_initialize()","detected session in URL",a,"redirect type",n),await this._saveSession(a),setTimeout(async()=>{"recovery"===n?await this._notifyAllSubscribers("PASSWORD_RECOVERY",a):await this._notifyAllSubscribers("SIGNED_IN",a)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(u(e))return{error:e};return{error:new c("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{let{data:i,error:a}=await X(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!=(s=null==(t=null==e?void 0:e.options)?void 0:t.data)?s:{},gotrue_meta_security:{captcha_token:null==(r=null==e?void 0:e.options)?void 0:r.captchaToken}},xform:Q});if(a||!i)return{data:{user:null,session:null},error:a};let n=i.session,o=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",n)),{data:{user:o,session:n},error:null}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,s,r;try{let i;if("email"in e){let{email:s,password:r,options:a}=e,n=null,o=null;"pkce"===this.flowType&&([n,o]=await B(this.storage,this.storageKey)),i=await X(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==a?void 0:a.emailRedirectTo,body:{email:s,password:r,data:null!=(t=null==a?void 0:a.data)?t:{},gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken},code_challenge:n,code_challenge_method:o},xform:Q})}else if("phone"in e){let{phone:t,password:a,options:n}=e;i=await X(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:a,data:null!=(s=null==n?void 0:n.data)?s:{},channel:null!=(r=null==n?void 0:n.channel)?r:"sms",gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Q})}else throw new w("You must provide either an email or phone number and a password");let{data:a,error:n}=i;if(n||!a)return{data:{user:null,session:null},error:n};let o=a.session,l=a.user;return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:s,password:r,options:i}=e;t=await X(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:ee})}else if("phone"in e){let{phone:s,password:r,options:i}=e;t=await X(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:ee})}else throw new w("You must provide either an email or phone number and a password");let{data:s,error:r}=t;if(r)return{data:{user:null,session:null},error:r};if(!s||!s.session||!s.user)return{data:{user:null,session:null},error:new g};return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,s,r,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null==(t=e.options)?void 0:t.redirectTo,scopes:null==(s=e.options)?void 0:s.scopes,queryParams:null==(r=e.options)?void 0:r.queryParams,skipBrowserRedirect:null==(i=e.options)?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,r,i,a,n,o,l,h,c,d,f;let w,y;if("message"in e)w=e.message,y=e.signature;else{let u,{chain:d,wallet:f,statement:g,options:p}=e;if(x())if("object"==typeof f)u=f;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))u=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if("object"!=typeof f||!(null==p?void 0:p.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");u=f}let _=new URL(null!=(t=null==p?void 0:p.url)?t:window.location.href);if("signIn"in u&&u.signIn){let e,t=await u.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==p?void 0:p.signInWithSolana),{version:"1",domain:_.host,uri:_.href}),g?{statement:g}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)w="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),y=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in u)||"function"!=typeof u.signMessage||!("publicKey"in u)||"object"!=typeof u||!u.publicKey||!("toBase58"in u.publicKey)||"function"!=typeof u.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");w=[`${_.host} wants you to sign in with your Solana account:`,u.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${_.href}`,`Issued At: ${null!=(r=null==(s=null==p?void 0:p.signInWithSolana)?void 0:s.issuedAt)?r:new Date().toISOString()}`,...(null==(i=null==p?void 0:p.signInWithSolana)?void 0:i.notBefore)?[`Not Before: ${p.signInWithSolana.notBefore}`]:[],...(null==(a=null==p?void 0:p.signInWithSolana)?void 0:a.expirationTime)?[`Expiration Time: ${p.signInWithSolana.expirationTime}`]:[],...(null==(n=null==p?void 0:p.signInWithSolana)?void 0:n.chainId)?[`Chain ID: ${p.signInWithSolana.chainId}`]:[],...(null==(o=null==p?void 0:p.signInWithSolana)?void 0:o.nonce)?[`Nonce: ${p.signInWithSolana.nonce}`]:[],...(null==(l=null==p?void 0:p.signInWithSolana)?void 0:l.requestId)?[`Request ID: ${p.signInWithSolana.requestId}`]:[],...(null==(c=null==(h=null==p?void 0:p.signInWithSolana)?void 0:h.resources)?void 0:c.length)?["Resources",...p.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await u.signMessage(new TextEncoder().encode(w),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");y=e}}try{let{data:t,error:s}=await X(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:w,signature:function(e){let t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>I(e,s,r)),I(null,s,r),t.join("")}(y)},(null==(d=e.options)?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null==(f=e.options)?void 0:f.captchaToken}}:null),xform:Q});if(s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new g};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await $(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{let{data:t,error:i}=await X(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:Q});if(await q(this.storage,`${this.storageKey}-code-verifier`),i)throw i;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new g};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:i}}catch(e){if(u(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:s,token:r,access_token:i,nonce:a}=e,{data:n,error:o}=await X(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:a,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:Q});if(o)return{data:{user:null,session:null},error:o};if(!n||!n.session||!n.user)return{data:{user:null,session:null},error:new g};return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",n.session)),{data:n,error:o}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,s,r,i,a;try{if("email"in e){let{email:r,options:i}=e,a=null,n=null;"pkce"===this.flowType&&([a,n]=await B(this.storage,this.storageKey));let{error:o}=await X(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!=(t=null==i?void 0:i.data)?t:{},create_user:null==(s=null==i?void 0:i.shouldCreateUser)||s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:a,code_challenge_method:n},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:s}=e,{data:n,error:o}=await X(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!=(r=null==s?void 0:s.data)?r:{},create_user:null==(i=null==s?void 0:s.shouldCreateUser)||i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!=(a=null==s?void 0:s.channel)?a:"sms"}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new w("You must provide either an email or phone number.")}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,s;try{let r,i;"options"in e&&(r=null==(t=e.options)?void 0:t.redirectTo,i=null==(s=e.options)?void 0:s.captchaToken);let{data:a,error:n}=await X(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:Q});if(n)throw n;if(!a)throw Error("An error occurred on token verification.");let o=a.session,l=a.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,s,r;try{let i=null,a=null;return"pkce"===this.flowType&&([i,a]=await B(this.storage,this.storageKey)),await X(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!=(s=null==(t=e.options)?void 0:t.redirectTo)?s:void 0}),(null==(r=null==e?void 0:e.options)?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:a}),headers:this.headers,xform:es})}catch(e){if(u(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new f;let{error:r}=await X(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:s,type:r,options:i}=e,{error:a}=await X(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:s,type:r,options:i}=e,{data:a,error:n}=await X(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:n}}throw new w("You must provide either an email or phone number and a type")}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await $(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let s=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}let{session:r,error:i}=await this._callRefreshToken(e.refresh_token);if(i)return{data:{session:null},error:i};return{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await X(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:et});return await this._useSession(async e=>{var t,s,r;let{data:i,error:a}=e;if(a)throw a;return(null==(t=i.session)?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await X(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0,xform:et}):{data:{user:null},error:new f}})}catch(e){if(u(e))return u(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await q(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{let{data:r,error:i}=s;if(i)throw i;if(!r.session)throw new f;let a=r.session,n=null,o=null;"pkce"===this.flowType&&null!=e.email&&([n,o]=await B(this.storage,this.storageKey));let{data:l,error:u}=await X(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:n,code_challenge_method:o}),jwt:a.access_token,xform:et});if(u)throw u;return a.user=l.user,await this._saveSession(a),await this._notifyAllSubscribers("USER_UPDATED",a),{data:{user:a.user},error:null}})}catch(e){if(u(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new f;let t=Date.now()/1e3,s=t,r=!0,i=null,{payload:a}=L(e.access_token);if(a.exp&&(r=(s=a.exp)<=t),r){let{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};i=t}else{let{data:r,error:a}=await this._getUser(e.access_token);if(a)throw a;i={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(e){if(u(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){let{data:r,error:i}=t;if(i)throw i;e=null!=(s=r.session)?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new f;let{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(u(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!x())throw new y("No browser detected.");if(e.error||e.error_description||e.error_code)throw new y(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new p("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new y("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new p("No code detected.");let{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;let r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:a,expires_in:n,expires_at:o,token_type:l}=e;if(!i||!n||!a||!l)throw new y("No session defined in URL");let u=Math.round(Date.now()/1e3),h=parseInt(n),c=u+h;o&&(c=parseInt(o));let d=c-u;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${h}s`);let f=c-h;u-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,c,u):u-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,c,u);let{data:g,error:w}=await this._getUser(i);if(w)throw w;let _={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:h,expires_at:c,refresh_token:a,token_type:l,user:g.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:_,redirectType:e.type},error:null}}catch(e){if(u(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await $(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{error:i};let a=null==(s=r.session)?void 0:s.access_token;if(a){let{error:t}=await this.admin.signOut(a,e);if(t&&!(u(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await q(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{let{data:{session:r},error:i}=t;if(i)throw i;await (null==(s=this.stateChangeEmitters.get(e))?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(t){await (null==(r=this.stateChangeEmitters.get(e))?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await B(this.storage,this.storageKey,!0));try{return await X(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(u(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!=(e=t.user.identities)?e:[]},error:null}}catch(e){if(u(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:s,error:r}=await this._useSession(async t=>{var s,r,i,a,n;let{data:o,error:l}=t;if(l)throw l;let u=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null==(s=e.options)?void 0:s.redirectTo,scopes:null==(r=e.options)?void 0:r.scopes,queryParams:null==(i=e.options)?void 0:i.queryParams,skipBrowserRedirect:!0});return await X(this.fetch,"GET",u,{headers:this.headers,jwt:null!=(n=null==(a=o.session)?void 0:a.access_token)?n:void 0})});if(r)throw r;return!x()||(null==(t=e.options)?void 0:t.skipBrowserRedirect)||window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(t){if(u(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:a}=t;if(a)throw a;return await X(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!=(r=null==(s=i.session)?void 0:s.access_token)?r:void 0})})}catch(e){if(u(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var s,r;let i=Date.now();return await (s=async s=>(s>0&&await D(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await X(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:Q})),r=(e,t)=>{let s=200*Math.pow(2,e);return t&&b(t)&&Date.now()+s-i<3e4},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{let t=await s(i);if(!r(i,null,t))return void e(t)}catch(e){if(!r(i,e))return void t(e)}})()}))}catch(e){if(this._debug(t,"error",e),u(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),x()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let s=await $(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s)){this._debug(t,"session is not valid"),null!==s&&await this._removeSession();return}let r=(null!=(e=s.expires_at)?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&s.refresh_token){let{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),b(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new f;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new U;let{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new f;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(e){if(this._debug(r,"error",e),u(e)){let s={session:null,error:e};return b(e)||await this._removeSession(),null==(t=this.refreshingDeferred)||t.resolve(s),s}throw null==(s=this.refreshingDeferred)||s.reject(e),e}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){let r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});let r=[],i=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(e){r.push(e)}});if(await Promise.all(i),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await P(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await q(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&x()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:s}}=t;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");let r=Math.floor((1e3*s.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof ec)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!x()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState)return void this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){let r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){let[e,t]=await B(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){let e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await X(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if(u(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;let{data:i,error:a}=t;if(a)return{data:null,error:a};let n=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await X(this.fetch,"POST",`${this.url}/factors`,{body:n,headers:this.headers,jwt:null==(s=null==i?void 0:i.session)?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null==(r=null==o?void 0:o.totp)?void 0:r.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(u(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;if(i)return{data:null,error:i};let{data:a,error:n}=await X(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token});return n?{data:null,error:n}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+a.expires_in},a)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",a),{data:a,error:n})})}catch(e){if(u(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;let{data:r,error:i}=t;return i?{data:null,error:i}:await X(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null==(s=null==r?void 0:r.session)?void 0:s.access_token})})}catch(e){if(u(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let s=(null==e?void 0:e.factors)||[],r=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),i=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:r,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;let{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:a}=L(r.access_token),n=null;a.aal&&(n=a.aal);let o=n;return(null!=(s=null==(t=r.user.factors)?void 0:t.filter(e=>"verified"===e.status))?s:[]).length>0&&(o="aal2"),{data:{currentLevel:n,nextLevel:o,currentAuthenticationMethods:a.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(t=>t.kid===e);if(s||(s=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return s;let{data:r,error:i}=await X(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!r.keys||0===r.keys.length)throw new m("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),!(s=r.keys.find(t=>t.kid===e)))throw new m("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:a,signature:n,raw:{header:o,payload:l}}=L(r);var s=a.exp;if(!s)throw Error("Missing exp claim");if(s<=Math.floor(Date.now()/1e3))throw Error("JWT has expired");if(!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:a,header:i,signature:n},error:null}}let u=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),h=await this.fetchJwk(i.kid,t),c=await crypto.subtle.importKey("jwk",h,u,!0,["verify"]);if(!await crypto.subtle.verify(u,c,n,function(e){let t=[];return function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){let t=(r-55296)*1024&65535;r=(e.charCodeAt(s+1)-56320&65535|t)+65536,s+=1}!function(e,t){if(e<=127)return t(e);if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(r,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${l}`)))throw new m("Invalid JWT signature");return{data:{claims:a,header:i,signature:n},error:null}}catch(e){if(u(e))return{data:null,error:e};throw e}}}ey.nextInstanceID=0;let ep=ey},80414:(e,t,s)=>{var r;s.d(t,{FS:()=>u});let i=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(s.bind(s,92410)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class a extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class n extends a{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class o extends a{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class l extends a{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(r||(r={}));class u{constructor(e,{headers:t={},customFetch:s,region:a=r.Any}={}){this.url=e,this.headers=t,this.region=a,this.fetch=i(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s,r,i,a,u;return r=this,i=void 0,a=void 0,u=function*(){try{let r,i,{headers:a,method:u,body:h}=t,c={},{region:d}=t;d||(d=this.region);let f=new URL(`${this.url}/${e}`);d&&"any"!==d&&(c["x-region"]=d,f.searchParams.set("forceFunctionRegion",d)),h&&(a&&!Object.prototype.hasOwnProperty.call(a,"Content-Type")||!a)&&("undefined"!=typeof Blob&&h instanceof Blob||h instanceof ArrayBuffer?(c["Content-Type"]="application/octet-stream",r=h):"string"==typeof h?(c["Content-Type"]="text/plain",r=h):"undefined"!=typeof FormData&&h instanceof FormData?r=h:(c["Content-Type"]="application/json",r=JSON.stringify(h)));let g=yield this.fetch(f.toString(),{method:u||"POST",headers:Object.assign(Object.assign(Object.assign({},c),this.headers),a),body:r}).catch(e=>{throw new n(e)}),w=g.headers.get("x-relay-error");if(w&&"true"===w)throw new o(g);if(!g.ok)throw new l(g);let y=(null!=(s=g.headers.get("Content-Type"))?s:"text/plain").split(";")[0].trim();return{data:"application/json"===y?yield g.json():"application/octet-stream"===y?yield g.blob():"text/event-stream"===y?g:"multipart/form-data"===y?yield g.formData():yield g.text(),error:null,response:g}}catch(e){return{data:null,error:e,response:e instanceof l||e instanceof o?e.context:void 0}}},new(a||(a=Promise))(function(e,t){function s(e){try{o(u.next(e))}catch(e){t(e)}}function n(e){try{o(u.throw(e))}catch(e){t(e)}}function o(t){var r;t.done?e(t.value):((r=t.value)instanceof a?r:new a(function(e){e(r)})).then(s,n)}o((u=u.apply(r,i||[])).next())})}}}}]);