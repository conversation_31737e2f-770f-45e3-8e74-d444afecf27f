{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "NX5T4m4JfWh0z2i6pmZsk", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EOLMuSNB25Lw8T36SdvfzjajKjIsmvgFp+SQnzCGdD4=", "__NEXT_PREVIEW_MODE_ID": "4258dd4abe03db691f6f88cdaf6c0766", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4b36d424cc2c41173524b9f7270b79c761563303b2181e02390126127010a3c4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "320b720134822cae239893637b1294120caec43a21b113d9f4de1b837c1b44b1"}}}, "functions": {}, "sortedMiddleware": ["/"]}