{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "bFFOqpAoUoP4vWNzBOgrv", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EOLMuSNB25Lw8T36SdvfzjajKjIsmvgFp+SQnzCGdD4=", "__NEXT_PREVIEW_MODE_ID": "273b3e16aebb1d43af323eef60318cb2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5bde615afe1501b4bf73466bc28e53fdf58b1ffb847b7fbfe95ab0c9c041bece", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f5b523eb0ac46accfe8f6741e27b278b21cfc751a03f16dcf0f0e8b4696ac07a"}}}, "functions": {}, "sortedMiddleware": ["/"]}