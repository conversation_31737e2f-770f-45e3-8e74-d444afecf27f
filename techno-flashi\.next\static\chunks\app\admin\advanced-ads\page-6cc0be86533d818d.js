(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3630],{15915:(e,s,a)=>{Promise.resolve().then(a.bind(a,63948))},63948:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var t=a(95155),l=a(12115),r=a(15504);function i(e){let{ad:s,onSave:a,onCancel:r,isOpen:i}=e,[d,n]=(0,l.useState)({name:"",description:"",ad_type:"banner",ad_format:"html",network:"",html_content:"",css_content:"",javascript_content:"",image_url:"",video_url:"",click_url:"",position:"header",container_id:"",z_index:1e3,target_pages:["*"],target_devices:["desktop","mobile","tablet"],target_countries:[],start_date:"",end_date:"",schedule_days:[0,1,2,3,4,5,6],schedule_hours:[],enabled:!0,priority:5,max_impressions:void 0,max_clicks:void 0,frequency_cap:void 0,responsive_breakpoints:{mobile:768,tablet:1024},mobile_html:"",tablet_html:"",animation_type:"",animation_duration:300,hover_effects:{},ab_test_group:"",ab_test_weight:100,tags:[]}),[c,o]=(0,l.useState)(!1),[m,x]=(0,l.useState)("basic");(0,l.useEffect)(()=>{s&&n({name:s.name||"",description:s.description||"",ad_type:s.ad_type,ad_format:s.ad_format,network:s.network||"",html_content:s.html_content||"",css_content:s.css_content||"",javascript_content:s.javascript_content||"",image_url:s.image_url||"",video_url:s.video_url||"",click_url:s.click_url||"",position:s.position,container_id:s.container_id||"",z_index:s.z_index,target_pages:s.target_pages,target_devices:s.target_devices,target_countries:s.target_countries,start_date:s.start_date?s.start_date.split("T")[0]:"",end_date:s.end_date?s.end_date.split("T")[0]:"",schedule_days:s.schedule_days,schedule_hours:s.schedule_hours,enabled:s.enabled,priority:s.priority,max_impressions:s.max_impressions,max_clicks:s.max_clicks,frequency_cap:s.frequency_cap,responsive_breakpoints:s.responsive_breakpoints,mobile_html:s.mobile_html||"",tablet_html:s.tablet_html||"",animation_type:s.animation_type||"",animation_duration:s.animation_duration,hover_effects:s.hover_effects,ab_test_group:s.ab_test_group||"",ab_test_weight:s.ab_test_weight,tags:s.tags})},[s]);let u=async e=>{e.preventDefault(),o(!0);try{var s;let e={...d,start_date:d.start_date&&""!==d.start_date.trim()?d.start_date+"T00:00:00Z":void 0,end_date:d.end_date&&""!==d.end_date.trim()?d.end_date+"T23:59:59Z":void 0,max_impressions:d.max_impressions||void 0,max_clicks:d.max_clicks||void 0,frequency_cap:d.frequency_cap||void 0,name:d.name.trim(),description:(null==(s=d.description)?void 0:s.trim())||""};if(!e.name)return void alert("❌ Ad name is required");if(!e.ad_type)return void alert("❌ Ad type is required");if(!e.position)return void alert("❌ Ad position is required");await a(e)}catch(e){if(console.error("Error saving ad:",e),e&&"object"==typeof e&&"message"in e){let s=e.message;s.includes("timestamp")?alert("❌ Invalid date format. Please check your start and end dates."):s.includes("duplicate")?alert("❌ An ad with this name already exists. Please choose a different name."):alert("❌ Error saving ad: ".concat(s))}else alert("❌ Error saving ad. Please try again.")}finally{o(!1)}},h=(e,s)=>{n(a=>({...a,[e]:s}))};return i?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-blue-600 text-white p-6",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:s?"✏️ Edit Advertisement":"➕ Create New Advertisement"}),(0,t.jsx)("button",{onClick:r,className:"text-white hover:text-gray-200 text-2xl",children:"✕"})]})}),(0,t.jsx)("div",{className:"border-b bg-gray-50",children:(0,t.jsx)("nav",{className:"flex space-x-8 px-6",children:[{id:"basic",label:"\uD83D\uDCCB Basic Info",icon:"\uD83D\uDCCB"},{id:"content",label:"\uD83C\uDFA8 Content",icon:"\uD83C\uDFA8"},{id:"targeting",label:"\uD83C\uDFAF Targeting",icon:"\uD83C\uDFAF"},{id:"advanced",label:"⚙️ Advanced",icon:"⚙️"}].map(e=>(0,t.jsx)("button",{onClick:()=>x(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(m===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:e.label},e.id))})}),(0,t.jsxs)("form",{onSubmit:u,className:"flex flex-col h-full",children:[(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:["basic"===m&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCDD Ad Name *"}),(0,t.jsx)("input",{type:"text",value:d.name,onChange:e=>h("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Enter ad name",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDF10 Network"}),(0,t.jsxs)("select",{value:d.network,onChange:e=>h("network",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"",children:"Select Network"}),(0,t.jsx)("option",{value:"monetag",children:"Monetag"}),(0,t.jsx)("option",{value:"adsense",children:"Google AdSense"}),(0,t.jsx)("option",{value:"custom",children:"Custom"}),(0,t.jsx)("option",{value:"affiliate",children:"Affiliate"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFAF Ad Type *"}),(0,t.jsxs)("select",{value:d.ad_type,onChange:e=>h("ad_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,t.jsx)("option",{value:"banner",children:"Banner"}),(0,t.jsx)("option",{value:"video",children:"Video"}),(0,t.jsx)("option",{value:"interactive",children:"Interactive"}),(0,t.jsx)("option",{value:"text",children:"Text"}),(0,t.jsx)("option",{value:"native",children:"Native"}),(0,t.jsx)("option",{value:"popup",children:"Popup"}),(0,t.jsx)("option",{value:"interstitial",children:"Interstitial"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCD Position *"}),(0,t.jsxs)("select",{value:d.position,onChange:e=>h("position",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,t.jsx)("option",{value:"header",children:"Header"}),(0,t.jsx)("option",{value:"sidebar",children:"Sidebar"}),(0,t.jsx)("option",{value:"footer",children:"Footer"}),(0,t.jsx)("option",{value:"in-content",children:"In-Content"}),(0,t.jsx)("option",{value:"popup",children:"Popup"}),(0,t.jsx)("option",{value:"floating",children:"Floating"}),(0,t.jsx)("option",{value:"sticky",children:"Sticky"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"⭐ Priority (1-10)"}),(0,t.jsx)("input",{type:"number",min:"1",max:"10",value:d.priority,onChange:e=>h("priority",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"enabled",checked:d.enabled,onChange:e=>h("enabled",e.target.checked),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"enabled",className:"text-sm font-medium text-gray-700",children:"✅ Enable Ad"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCDD Description"}),(0,t.jsx)("textarea",{value:d.description,onChange:e=>h("description",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"Enter ad description"})]})]}),"content"===m&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCBB HTML Content"}),(0,t.jsx)("textarea",{value:d.html_content,onChange:e=>h("html_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:8,placeholder:"Enter HTML content..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFA8 CSS Content"}),(0,t.jsx)("textarea",{value:d.css_content,onChange:e=>h("css_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Enter CSS styles..."})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"⚡ JavaScript Content"}),(0,t.jsx)("textarea",{value:d.javascript_content,onChange:e=>h("javascript_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Enter JavaScript code..."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDDBC️ Image URL"}),(0,t.jsx)("input",{type:"url",value:d.image_url,onChange:e=>h("image_url",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/image.jpg"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDD17 Click URL"}),(0,t.jsx)("input",{type:"url",value:d.click_url,onChange:e=>h("click_url",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/landing-page"})]})]})]}),"targeting"===m&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC4 Target Pages"}),(0,t.jsx)("input",{type:"text",value:d.target_pages.join(", "),onChange:e=>h("target_pages",e.target.value.split(",").map(e=>e.trim()).filter(e=>e)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"*, /articles, /ai-tools"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Use * for all pages, or specify paths separated by commas"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCF1 Target Devices"}),(0,t.jsx)("div",{className:"flex space-x-4",children:["desktop","mobile","tablet"].map(e=>(0,t.jsxs)("label",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",checked:d.target_devices.includes(e),onChange:s=>{h("target_devices",s.target.checked?[...d.target_devices,e]:d.target_devices.filter(s=>s!==e))},className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,t.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e})]},e))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC5 Start Date"}),(0,t.jsx)("input",{type:"date",value:d.start_date,onChange:e=>h("start_date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC5 End Date"}),(0,t.jsx)("input",{type:"date",value:d.end_date,onChange:e=>h("end_date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]})]})]}),"advanced"===m&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCA Max Impressions"}),(0,t.jsx)("input",{type:"number",value:d.max_impressions||"",onChange:e=>h("max_impressions",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Unlimited"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDDB1️ Max Clicks"}),(0,t.jsx)("input",{type:"number",value:d.max_clicks||"",onChange:e=>h("max_clicks",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Unlimited"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDD04 Frequency Cap"}),(0,t.jsx)("input",{type:"number",value:d.frequency_cap||"",onChange:e=>h("frequency_cap",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Per day"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFF7️ Tags"}),(0,t.jsx)("input",{type:"text",value:d.tags.join(", "),onChange:e=>h("tags",e.target.value.split(",").map(e=>e.trim()).filter(e=>e)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"monetag, banner, test"})]})]})]}),(0,t.jsxs)("div",{className:"border-t bg-gray-50 px-6 py-4 flex justify-end space-x-3",children:[(0,t.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:c,children:"❌ Cancel"}),(0,t.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",disabled:c,children:c?"⏳ Saving...":s?"\uD83D\uDCBE Update Ad":"➕ Create Ad"})]})]})]})}):null}function d(){let[e,s]=(0,l.useState)("dashboard"),[a,d]=(0,l.useState)([]),[n,c]=(0,l.useState)([]),[o,m]=(0,l.useState)(!0),[x,u]=(0,l.useState)(null),[h,g]=(0,l.useState)(!1),[b,p]=(0,l.useState)(""),[v,j]=(0,l.useState)("all"),[y,f]=(0,l.useState)("all"),[N,_]=(0,l.useState)("all");(0,l.useEffect)(()=>{(async()=>{m(!0);try{let[e,s]=await Promise.all([(0,r.JY)(),(0,r.H1)()]);d(e),c(s)}catch(e){console.error("Error loading data:",e)}finally{m(!1)}})()},[]);let w=a.filter(e=>{let s=e.name.toLowerCase().includes(b.toLowerCase())||(e.description||"").toLowerCase().includes(b.toLowerCase()),a="all"===v||e.network===v,t="all"===y||e.position===y,l="all"===N||"enabled"===N&&e.enabled||"disabled"===N&&!e.enabled;return s&&a&&t&&l}),D=async e=>{try{let s=await (0,r.Am)(e);s?(d(e=>[...e,s]),g(!1),alert("✅ Ad created successfully!")):alert("❌ Failed to create ad. Please try again.")}catch(e){console.error("Error creating ad:",e),alert("❌ Error creating ad. Please check the console for details.")}},C=async e=>{if(x)try{let s=await (0,r.dT)(x.id,e);s?(d(e=>e.map(e=>e.id===x.id?s:e)),u(null),alert("✅ Ad updated successfully!")):alert("❌ Failed to update ad. Please try again.")}catch(e){console.error("Error updating ad:",e),alert("❌ Error updating ad. Please check the console for details.")}},k=async e=>{confirm("Are you sure you want to delete this ad?")&&await (0,r.nt)(e)&&d(s=>s.filter(s=>s.id!==e))},A=async e=>{try{let s=await (0,r.dT)(e.id,{enabled:!e.enabled});s&&d(a=>a.map(a=>a.id===e.id?s:a))}catch(e){console.error("Error toggling ad:",e),alert("❌ Error toggling ad status.")}},S={totalAds:a.length,activeAds:a.filter(e=>e.enabled).length,inactiveAds:a.filter(e=>!e.enabled).length,networks:Array.from(new Set(a.map(e=>e.network).filter(Boolean))).length,positions:Array.from(new Set(a.map(e=>e.position))).length,templates:n.length};return o?(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,t.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading Advanced Advertising Dashboard..."})]})})}):(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\uD83C\uDFAF Advanced Advertising Dashboard"}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Comprehensive advertising management system with multi-network support"})]}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("button",{onClick:()=>g(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 font-medium",children:"➕ Create New Ad"}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:"\uD83D\uDD04 Refresh"})]})]})})}),(0,t.jsx)("div",{className:"bg-white border-b",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("nav",{className:"flex space-x-8",children:[{id:"dashboard",label:"\uD83D\uDCCA Dashboard",icon:"\uD83D\uDCCA"},{id:"ads",label:"\uD83C\uDFAF Ads (".concat(S.totalAds,")"),icon:"\uD83C\uDFAF"},{id:"templates",label:"\uD83D\uDCCB Templates (".concat(S.templates,")"),icon:"\uD83D\uDCCB"},{id:"analytics",label:"\uD83D\uDCC8 Analytics",icon:"\uD83D\uDCC8"},{id:"settings",label:"⚙️ Settings",icon:"⚙️"}].map(a=>(0,t.jsx)("button",{onClick:()=>s(a.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(e===a.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:a.label},a.id))})})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["dashboard"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-blue-600 font-semibold",children:"\uD83C\uDFAF"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Ads"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:S.totalAds})]})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-green-600 font-semibold",children:"✅"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Active Ads"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:S.activeAds})]})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-yellow-600 font-semibold",children:"\uD83C\uDF10"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Networks"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:S.networks})]})]})}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-purple-600 font-semibold",children:"\uD83D\uDCCB"})})}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Templates"}),(0,t.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:S.templates})]})]})})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDE80 Quick Actions"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)("button",{onClick:()=>g(!0),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"➕"}),(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Create New Ad"}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Start from scratch"})]})}),(0,t.jsx)("button",{onClick:()=>s("templates"),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCCB"}),(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"Use Template"}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Quick setup"})]})}),(0,t.jsx)("button",{onClick:()=>s("analytics"),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCC8"}),(0,t.jsx)("div",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:"Performance data"})]})})]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCCB Recent Ads"}),(0,t.jsx)("div",{className:"space-y-3",children:a.slice(0,5).map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.enabled?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:[e.network," • ",e.position]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{onClick:()=>A(e),className:"px-3 py-1 rounded text-xs font-medium ".concat(e.enabled?"bg-yellow-100 text-yellow-800 hover:bg-yellow-200":"bg-green-100 text-green-800 hover:bg-green-200"),children:e.enabled?"⏸️ Pause":"▶️ Enable"}),(0,t.jsx)("button",{onClick:()=>{u(e),g(!0)},className:"px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium hover:bg-blue-200",children:"✏️ Edit"})]})]},e.id))})]})]}),"ads"===e&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,t.jsx)("input",{type:"text",value:b,onChange:e=>p(e.target.value),placeholder:"Search ads...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Network"}),(0,t.jsxs)("select",{value:v,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Networks"}),(0,t.jsx)("option",{value:"monetag",children:"Monetag"}),(0,t.jsx)("option",{value:"adsense",children:"Google AdSense"}),(0,t.jsx)("option",{value:"custom",children:"Custom"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Position"}),(0,t.jsxs)("select",{value:y,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Positions"}),(0,t.jsx)("option",{value:"header",children:"Header"}),(0,t.jsx)("option",{value:"sidebar",children:"Sidebar"}),(0,t.jsx)("option",{value:"footer",children:"Footer"}),(0,t.jsx)("option",{value:"in-content",children:"In-Content"}),(0,t.jsx)("option",{value:"floating",children:"Floating"}),(0,t.jsx)("option",{value:"popup",children:"Popup"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,t.jsxs)("select",{value:N,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)("option",{value:"all",children:"All Status"}),(0,t.jsx)("option",{value:"enabled",children:"Enabled"}),(0,t.jsx)("option",{value:"disabled",children:"Disabled"})]})]})]})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,t.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,t.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Ads (",w.length,")"]})}),(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:w.map(e=>(0,t.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.enabled?"bg-green-500":"bg-red-500")}),(0,t.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,t.jsx)("span",{className:"px-2 py-1 rounded text-xs font-medium ".concat(e.enabled?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.enabled?"✅ Active":"❌ Inactive"})]}),(0,t.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e.description}),(0,t.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,t.jsxs)("span",{children:["\uD83C\uDF10 ",e.network||"Custom"]}),(0,t.jsxs)("span",{children:["\uD83D\uDCCD ",e.position]}),(0,t.jsxs)("span",{children:["\uD83C\uDFAF ",e.ad_type]}),(0,t.jsxs)("span",{children:["⭐ Priority: ",e.priority]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("button",{onClick:()=>A(e),className:"px-3 py-1 rounded text-sm font-medium ".concat(e.enabled?"bg-yellow-100 text-yellow-800 hover:bg-yellow-200":"bg-green-100 text-green-800 hover:bg-green-200"),children:e.enabled?"⏸️ Pause":"▶️ Enable"}),(0,t.jsx)("button",{onClick:()=>{u(e),g(!0)},className:"px-3 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium hover:bg-blue-200",children:"✏️ Edit"}),(0,t.jsx)("button",{onClick:()=>k(e.id),className:"px-3 py-1 bg-red-100 text-red-800 rounded text-sm font-medium hover:bg-red-200",children:"\uD83D\uDDD1️ Delete"})]})]})},e.id))})]})]}),"templates"===e&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCCB Ad Templates"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium",children:e.category})]}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,t.jsxs)("span",{children:["\uD83C\uDF10 ",e.network||"Custom"]}),(0,t.jsxs)("span",{children:["\uD83D\uDCCA Used ",e.usage_count," times"]})]}),(0,t.jsx)("button",{onClick:()=>{},className:"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 font-medium",children:"\uD83D\uDE80 Use Template"})]},e.id))})]})}),"analytics"===e&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCC8 Analytics Dashboard"}),(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCCA"}),(0,t.jsx)("h4",{className:"text-xl font-medium text-gray-900 mb-2",children:"Analytics Coming Soon"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Comprehensive analytics and performance tracking will be available here."})]})]})}),"settings"===e&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"⚙️ System Settings"}),(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"⚙️"}),(0,t.jsx)("h4",{className:"text-xl font-medium text-gray-900 mb-2",children:"Settings Panel"}),(0,t.jsx)("p",{className:"text-gray-500",children:"Global advertising settings and configuration options will be available here."})]})]})})]}),(0,t.jsx)(i,{ad:x,onSave:x?C:D,onCancel:()=>{g(!1),u(null)},isOpen:h||null!==x})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>s(15915)),_N_E=e.O()}]);