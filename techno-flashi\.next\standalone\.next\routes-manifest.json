{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}, {"key": "Content-Language", "value": "ar"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://fonts.googleapis.com https://fonts.gstatic.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://fonts.gstatic.com; img-src 'self' data: https: blob:; font-src 'self' https://fonts.gstatic.com https://fonts.googleapis.com data:; connect-src 'self' https: wss: https://zgktrwpladrkhhemhnni.supabase.co; object-src 'none'; frame-ancestors 'self'; base-uri 'self'; form-action 'self'; media-src 'self' https: data:;"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}], "regex": "^/api(?:/(.*))(?:/)?$"}, {"source": "/articles/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300, stale-while-revalidate=600"}], "regex": "^/articles(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/ai-tools/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300, stale-while-revalidate=600"}], "regex": "^/ai-tools(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/", "headers": [{"key": "Cache-Control", "value": "public, max-age=300, s-maxage=300, stale-while-revalidate=600"}], "regex": "^/(?:/)?$"}, {"source": "/static/:path*", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/static(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/:path*\\.(woff|woff2|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?\\.(woff|woff2|ttf|eot)(?:/)?$"}, {"source": "/:path*\\.(css|js)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?\\.(css|js)(?:/)?$"}, {"source": "/ads.txt", "headers": [{"key": "Content-Type", "value": "text/plain; charset=utf-8"}, {"key": "Cache-Control", "value": "public, max-age=3600"}], "regex": "^/ads\\.txt(?:/)?$"}, {"source": "/favicon.ico", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/favicon\\.ico(?:/)?$"}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}], "regex": "^/_next/static(?:/(.*))(?:/)?$"}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400, s-maxage=86400"}], "regex": "^/images(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/admin/ads/[id]/edit", "regex": "^/admin/ads/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/ads/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/admin/ai-tools/edit/[id]", "regex": "^/admin/ai\\-tools/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/ai\\-tools/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/articles/edit/[id]", "regex": "^/admin/articles/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/articles/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/pages/edit/[id]", "regex": "^/admin/pages/edit/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/pages/edit/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/admin/services/[id]/edit", "regex": "^/admin/services/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/admin/services/(?<nxtPid>[^/]+?)/edit(?:/)?$"}, {"page": "/ai-tools/[slug]", "regex": "^/ai\\-tools/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/ai\\-tools/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/api/ads/[id]", "regex": "^/api/ads/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/ads/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/ads/[id]/click", "regex": "^/api/ads/([^/]+?)/click(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/ads/(?<nxtPid>[^/]+?)/click(?:/)?$"}, {"page": "/api/pages/[id]", "regex": "^/api/pages/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/pages/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/services/[id]", "regex": "^/api/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/articles/[slug]", "regex": "^/articles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/articles/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/page/[slug]", "regex": "^/page/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/page/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/services/[id]", "regex": "^/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/services/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/ads", "regex": "^/admin/ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads(?:/)?$"}, {"page": "/admin/ads/ai-tools", "regex": "^/admin/ads/ai\\-tools(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads/ai\\-tools(?:/)?$"}, {"page": "/admin/ads/create", "regex": "^/admin/ads/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads/create(?:/)?$"}, {"page": "/admin/ads/migrate", "regex": "^/admin/ads/migrate(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads/migrate(?:/)?$"}, {"page": "/admin/ads/new", "regex": "^/admin/ads/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads/new(?:/)?$"}, {"page": "/admin/ads/sync", "regex": "^/admin/ads/sync(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ads/sync(?:/)?$"}, {"page": "/admin/advanced-ads", "regex": "^/admin/advanced\\-ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/advanced\\-ads(?:/)?$"}, {"page": "/admin/ai-tools", "regex": "^/admin/ai\\-tools(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ai\\-tools(?:/)?$"}, {"page": "/admin/ai-tools/new", "regex": "^/admin/ai\\-tools/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ai\\-tools/new(?:/)?$"}, {"page": "/admin/articles", "regex": "^/admin/articles(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/articles(?:/)?$"}, {"page": "/admin/articles/create", "regex": "^/admin/articles/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/articles/create(?:/)?$"}, {"page": "/admin/articles/new", "regex": "^/admin/articles/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/articles/new(?:/)?$"}, {"page": "/admin/media", "regex": "^/admin/media(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/media(?:/)?$"}, {"page": "/admin/pages", "regex": "^/admin/pages(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/pages(?:/)?$"}, {"page": "/admin/pages/new", "regex": "^/admin/pages/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/pages/new(?:/)?$"}, {"page": "/admin/services", "regex": "^/admin/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services(?:/)?$"}, {"page": "/admin/services/new", "regex": "^/admin/services/new(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/services/new(?:/)?$"}, {"page": "/admin/setup-ads", "regex": "^/admin/setup\\-ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/setup\\-ads(?:/)?$"}, {"page": "/admin/supabase-ads", "regex": "^/admin/supabase\\-ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/supabase\\-ads(?:/)?$"}, {"page": "/admin/test-ads", "regex": "^/admin/test\\-ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/test\\-ads(?:/)?$"}, {"page": "/admin/test-ads-verification", "regex": "^/admin/test\\-ads\\-verification(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/test\\-ads\\-verification(?:/)?$"}, {"page": "/ads.txt", "regex": "^/ads\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/ads\\.txt(?:/)?$"}, {"page": "/ai-tools", "regex": "^/ai\\-tools(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-tools(?:/)?$"}, {"page": "/ai-tools/categories", "regex": "^/ai\\-tools/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-tools/categories(?:/)?$"}, {"page": "/ai-tools/compare", "regex": "^/ai\\-tools/compare(?:/)?$", "routeKeys": {}, "namedRegex": "^/ai\\-tools/compare(?:/)?$"}, {"page": "/articles", "regex": "^/articles(?:/)?$", "routeKeys": {}, "namedRegex": "^/articles(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/login", "regex": "^/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/login(?:/)?$"}, {"page": "/manifest.webmanifest", "regex": "^/manifest\\.webmanifest(?:/)?$", "routeKeys": {}, "namedRegex": "^/manifest\\.webmanifest(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/rss.xml", "regex": "^/rss\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/rss\\.xml(?:/)?$"}, {"page": "/seo-diagnosis", "regex": "^/seo\\-diagnosis(?:/)?$", "routeKeys": {}, "namedRegex": "^/seo\\-diagnosis(?:/)?$"}, {"page": "/services", "regex": "^/services(?:/)?$", "routeKeys": {}, "namedRegex": "^/services(?:/)?$"}, {"page": "/setup-admin", "regex": "^/setup\\-admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/setup\\-admin(?:/)?$"}, {"page": "/sitemap-articles.xml", "regex": "^/sitemap\\-articles\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-articles\\.xml(?:/)?$"}, {"page": "/sitemap-index.xml", "regex": "^/sitemap\\-index\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-index\\.xml(?:/)?$"}, {"page": "/sitemap-tools.xml", "regex": "^/sitemap\\-tools\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\-tools\\.xml(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/terms-of-use", "regex": "^/terms\\-of\\-use(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-use(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test-animated-ads", "regex": "^/test\\-animated\\-ads(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-animated\\-ads(?:/)?$"}, {"page": "/test-cleaner", "regex": "^/test\\-cleaner(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-cleaner(?:/)?$"}, {"page": "/test-icons", "regex": "^/test\\-icons(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-icons(?:/)?$"}, {"page": "/youtube", "regex": "^/youtube(?:/)?$", "routeKeys": {}, "namedRegex": "^/youtube(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap", "regex": "^/sitemap\\.xml(?:/)?$"}]}