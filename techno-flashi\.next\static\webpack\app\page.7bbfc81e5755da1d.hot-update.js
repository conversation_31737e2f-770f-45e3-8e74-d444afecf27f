"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة مع تحسينات جديدة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669',\n                        '#f97316',\n                        '#ea580c',\n                        '#dc2626',\n                        '#b91c1c',\n                        '#7c3aed',\n                        '#6d28d9'\n                    ];\n                    for(let i = 0; i < 200; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 2,\n                            vy: (Math.random() - 0.5) * 2,\n                            size: Math.random() * 8 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.8 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.008 + Math.random() * 0.04,\n                            rotationSpeed: (Math.random() - 0.5) * 0.05,\n                            magnetism: Math.random() * 0.8 + 0.6,\n                            trail: [],\n                            energy: Math.random() * 100 + 50,\n                            type: Math.floor(Math.random() * 3),\n                            phase: Math.random() * Math.PI * 2,\n                            amplitude: Math.random() * 20 + 10 // سعة الحركة الموجية\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم هالة تتبع الماوس المتوهجة المحسنة\n                    if (mouseRef.current.x !== 0 || mouseRef.current.y !== 0) {\n                        // هالة خارجية كبيرة\n                        const outerGlowSize = 150 + Math.sin(time * 2) * 30;\n                        const outerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, outerGlowSize);\n                        outerGradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 1.5) * 0.08, \")\"));\n                        outerGradient.addColorStop(0.2, \"rgba(236, 72, 153, \".concat(0.15 + Math.sin(time * 2) * 0.06, \")\"));\n                        outerGradient.addColorStop(0.4, \"rgba(251, 191, 36, \".concat(0.1 + Math.sin(time * 2.5) * 0.04, \")\"));\n                        outerGradient.addColorStop(0.6, \"rgba(6, 182, 212, \".concat(0.08 + Math.sin(time * 3) * 0.03, \")\"));\n                        outerGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = outerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, outerGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // هالة متوسطة\n                        const midGlowSize = 80 + Math.sin(time * 3) * 15;\n                        const midGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, midGlowSize);\n                        midGradient.addColorStop(0, \"rgba(139, 92, 246, \".concat(0.3 + Math.sin(time * 2.5) * 0.1, \")\"));\n                        midGradient.addColorStop(0.5, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 3) * 0.08, \")\"));\n                        midGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = midGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, midGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // دائرة مركزية نابضة\n                        const centerSize = 20 + Math.sin(time * 4) * 8;\n                        const centerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, centerSize);\n                        centerGradient.addColorStop(0, \"rgba(255, 255, 255, \".concat(0.9 + Math.sin(time * 5) * 0.1, \")\"));\n                        centerGradient.addColorStop(0.7, \"rgba(168, 85, 247, \".concat(0.6 + Math.sin(time * 4) * 0.2, \")\"));\n                        centerGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = centerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, centerSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم موجات متطورة تنتشر من موضع الماوس\n                        for(let i = 0; i < 5; i++){\n                            const waveRadius = 30 + (time * 80 + i * 40) % 250;\n                            const waveAlpha = Math.max(0, 0.4 - waveRadius / 250 * 0.4);\n                            if (waveAlpha > 0.01) {\n                                // موجة رئيسية\n                                ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(waveAlpha, \")\");\n                                ctx.lineWidth = 3 - waveRadius / 250 * 2;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, waveRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة ثانوية متداخلة\n                                const secondaryRadius = waveRadius * 0.7;\n                                const secondaryAlpha = waveAlpha * 0.6;\n                                ctx.strokeStyle = \"rgba(236, 72, 153, \".concat(secondaryAlpha, \")\");\n                                ctx.lineWidth = 2 - secondaryRadius / 250 * 1.5;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, secondaryRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة داخلية\n                                if (i % 2 === 0) {\n                                    const innerRadius = waveRadius * 0.4;\n                                    const innerAlpha = waveAlpha * 0.8;\n                                    ctx.strokeStyle = \"rgba(251, 191, 36, \".concat(innerAlpha, \")\");\n                                    ctx.lineWidth = 1.5;\n                                    ctx.beginPath();\n                                    ctx.arc(mouseX, mouseY, innerRadius, 0, Math.PI * 2);\n                                    ctx.stroke();\n                                }\n                            }\n                        }\n                    }\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 300); // زيادة نطاق التأثير\n                            if (influence > 0.05) {\n                                // تحسين الحركة والحجم حسب قرب الماوس\n                                const moveIntensity = influence * 15;\n                                const sizeMultiplier = 0.2 + influence * 0.8;\n                                const alpha = 0.02 + influence * 0.1;\n                                ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(alpha, \")\");\n                                ctx.lineWidth = 1 + influence * 2;\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * moveIntensity, y + Math.cos(time + row + col) * moveIntensity, hexSize * sizeMultiplier);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        // استخدام rect بدلاً من roundRect للتوافق مع جميع المتصفحات\n                        ctx.rect(-size / 2, -size / 2, size, size);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتطور والأكثر ذكاءً\n                            const mouseInfluence = 250; // نطاق تأثير أوسع\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                // قوة جذب متدرجة حسب المسافة\n                                const attractionForce = force * 0.012;\n                                particle.vx += Math.cos(angle) * attractionForce;\n                                particle.vy += Math.sin(angle) * attractionForce;\n                                // تأثير الطاقة المحسن مع تسارع\n                                particle.energy = Math.min(100, particle.energy + force * 6);\n                                // حركة موجية إضافية\n                                particle.phase += 0.1;\n                                const waveForce = Math.sin(particle.phase) * force * 0.003;\n                                particle.vx += Math.cos(angle + Math.PI / 2) * waveForce;\n                                particle.vy += Math.sin(angle + Math.PI / 2) * waveForce;\n                                // تأثير اهتزاز ذكي\n                                const vibrationIntensity = force * 0.004;\n                                particle.vx += (Math.random() - 0.5) * vibrationIntensity;\n                                particle.vy += (Math.random() - 0.5) * vibrationIntensity;\n                                // تسريع الدوران عند القرب من الماوس\n                                particle.rotationSpeed += force * 0.001;\n                            } else {\n                                // تقليل الطاقة والدوران تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.2);\n                                particle.rotationSpeed *= 0.98;\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية بأشكال مختلفة\n                            ctx.save();\n                            ctx.translate(particle.x, particle.y);\n                            ctx.rotate(particle.pulse * particle.rotationSpeed);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            const finalSize = Math.max(0.5, pulseSize);\n                            // رسم حسب نوع الجسيمة\n                            if (particle.type === 0) {\n                                // دائرة\n                                ctx.beginPath();\n                                ctx.arc(0, 0, finalSize, 0, Math.PI * 2);\n                                ctx.fill();\n                            } else if (particle.type === 1) {\n                                // مربع\n                                ctx.beginPath();\n                                ctx.rect(-finalSize, -finalSize, finalSize * 2, finalSize * 2);\n                                ctx.fill();\n                            } else {\n                                // نجمة\n                                ctx.beginPath();\n                                const spikes = 5;\n                                const outerRadius = finalSize;\n                                const innerRadius = finalSize * 0.5;\n                                for(let i = 0; i < spikes * 2; i++){\n                                    const radius = i % 2 === 0 ? outerRadius : innerRadius;\n                                    const angle = i * Math.PI / spikes;\n                                    const x = Math.cos(angle) * radius;\n                                    const y = Math.sin(angle) * radius;\n                                    if (i === 0) {\n                                        ctx.moveTo(x, y);\n                                    } else {\n                                        ctx.lineTo(x, y);\n                                    }\n                                }\n                                ctx.closePath();\n                                ctx.fill();\n                            }\n                            ctx.restore();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 552,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 559,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 564,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 569,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 603,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 609,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 598,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 562,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 550,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"BShlRgxf1Xjno/mi6QXyq9ZqIDE=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});