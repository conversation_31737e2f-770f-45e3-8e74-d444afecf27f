(()=>{var e={};e.id=6255,e.ids=[6255],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13369:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),i=t(43210),a=t(85814),n=t.n(a);function d(){let[e,r]=(0,i.useState)([]),[t,a]=(0,i.useState)(!0),[d,o]=(0,i.useState)(null),l=async()=>{try{a(!0);let e=await fetch("/api/services");if(!e.ok)throw Error("فشل في جلب الخدمات");let t=await e.json();r(t.services||[])}catch(e){console.error("Error fetching services:",e),o("حدث خطأ في جلب الخدمات")}finally{a(!1)}},c=async e=>{if(confirm("هل أنت متأكد من حذف هذه الخدمة؟"))try{if(!(await fetch(`/api/services/${e}`,{method:"DELETE"})).ok)throw Error("فشل في حذف الخدمة");l()}catch(e){console.error("Error deleting service:",e),alert("حدث خطأ في حذف الخدمة")}},x=e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{active:"bg-green-900 text-green-300",inactive:"bg-red-900 text-red-300",draft:"bg-yellow-900 text-yellow-300"}[e]||"bg-gray-800 text-gray-300"}`,children:{active:"نشط",inactive:"غير نشط",draft:"مسودة"}[e]||e}),m=e=>{switch(e.pricing_type){case"free":return"مجاني";case"paid":return e.pricing_amount?`${e.pricing_amount} ${e.pricing_currency}`:"مدفوع";default:return"حسب الطلب"}};return t?(0,s.jsx)("div",{className:"min-h-screen bg-dark-background flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري تحميل الخدمات..."})]})}):d?(0,s.jsx)("div",{className:"min-h-screen bg-dark-background flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-400 mb-4",children:d}),(0,s.jsx)("button",{onClick:l,className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90",children:"إعادة المحاولة"})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white",children:"إدارة الخدمات"}),(0,s.jsx)("p",{className:"text-dark-text-secondary mt-2",children:"إدارة وتحرير جميع الخدمات المتاحة في الموقع"})]}),(0,s.jsx)(n(),{href:"/admin/services/new",className:"bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200",children:"إضافة خدمة جديدة"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary mb-2",children:e.length}),(0,s.jsx)("div",{className:"text-dark-text-secondary",children:"إجمالي الخدمات"})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-500 mb-2",children:e.filter(e=>"active"===e.status).length}),(0,s.jsx)("div",{className:"text-dark-text-secondary",children:"خدمات نشطة"})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-500 mb-2",children:e.filter(e=>e.featured).length}),(0,s.jsx)("div",{className:"text-dark-text-secondary",children:"خدمات مميزة"})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-500 mb-2",children:e.filter(e=>"free"===e.pricing_type).length}),(0,s.jsx)("div",{className:"text-dark-text-secondary",children:"خدمات مجانية"})]})]}),0===e.length?(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg border border-gray-800 p-12 text-center",children:[(0,s.jsx)("div",{className:"text-gray-500 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-white mb-2",children:"لا توجد خدمات"}),(0,s.jsx)("p",{className:"text-dark-text-secondary mb-6",children:"ابدأ بإضافة خدمة جديدة لعرضها في الموقع"}),(0,s.jsx)(n(),{href:"/admin/services/new",className:"inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200",children:"إضافة خدمة جديدة"})]}):(0,s.jsx)("div",{className:"bg-dark-card rounded-lg border border-gray-800 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-700",children:[(0,s.jsx)("thead",{className:"bg-dark-background",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الخدمة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"التصنيف"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"السعر"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الحالة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{className:"bg-dark-card divide-y divide-gray-700",children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-800/50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-white flex items-center",children:[e.name,e.featured&&(0,s.jsx)("span",{className:"mr-2 bg-yellow-900 text-yellow-300 px-2 py-1 rounded-full text-xs",children:"مميز"})]}),(0,s.jsx)("div",{className:"text-sm text-dark-text-secondary",children:e.short_description||e.description.substring(0,50)+"..."})]})})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-white",children:e.category}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-white",children:m(e)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:x(e.status)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,s.jsx)(n(),{href:`/services/${e.id}`,className:"text-blue-600 hover:text-blue-900",target:"_blank",children:"عرض"}),(0,s.jsx)(n(),{href:`/admin/services/${e.id}/edit`,className:"text-indigo-600 hover:text-indigo-900",children:"تعديل"}),(0,s.jsx)("button",{onClick:()=>c(e.id),className:"text-red-600 hover:text-red-900",children:"حذف"})]})})]},e.id))})]})})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>n});var s=t(60687),i=t(63213),a=t(16189);function n({children:e}){let{user:r,loading:t}=(0,i.A)();return((0,a.useRouter)(),t)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,s.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}t(43210)},26379:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,r,t)=>{Promise.resolve().then(t.bind(t,67083))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60934:(e,r,t)=>{Promise.resolve().then(t.bind(t,13369))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66857:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),i=t(48088),a=t(88170),n=t.n(a),d=t(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(r,o);let l={children:["",{children:["admin",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,26379)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(t.bind(t,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\services\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/services/page",pathname:"/admin/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},67083:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},78238:(e,r,t)=>{Promise.resolve().then(t.bind(t,26379))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97579:(e,r,t)=>{Promise.resolve().then(t.bind(t,20769))},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413),i=t(67083);function a({children:e}){return(0,s.jsx)(i.ProtectedRoute,{children:(0,s.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}t(31240)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4447,6437,2675,3595],()=>t(66857));module.exports=s})();