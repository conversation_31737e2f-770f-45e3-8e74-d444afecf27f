"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ads/SimpleAdDisplay.tsx":
/*!************************************************!*\
  !*** ./src/components/ads/SimpleAdDisplay.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleAdDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleAdDisplay(param) {\n    let { position, className = '' } = param;\n    _s();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentAdIndex, setCurrentAdIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleAdDisplay.useEffect\": ()=>{\n            const fetchAds = {\n                \"SimpleAdDisplay.useEffect.fetchAds\": async ()=>{\n                    try {\n                        console.log(\"\\uD83D\\uDD0D SimpleAdDisplay: Fetching ads for position: \".concat(position));\n                        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('ads').select('*').eq('position', position).eq('enabled', true).order('priority', {\n                            ascending: false\n                        });\n                        if (error) {\n                            console.error('❌ SimpleAdDisplay: Error fetching ads:', error);\n                            setAds([]);\n                        } else {\n                            console.log(\"✅ SimpleAdDisplay: Found \".concat((data === null || data === void 0 ? void 0 : data.length) || 0, \" ads for position \").concat(position, \":\"), data);\n                            setAds(data || []);\n                        }\n                    } catch (err) {\n                        console.error('❌ SimpleAdDisplay: Error in fetchAds:', err);\n                        setAds([]);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"SimpleAdDisplay.useEffect.fetchAds\"];\n            fetchAds();\n        }\n    }[\"SimpleAdDisplay.useEffect\"], [\n        position\n    ]);\n    // Rotate ads every 10 seconds\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleAdDisplay.useEffect\": ()=>{\n            if (ads.length <= 1) return;\n            const interval = setInterval({\n                \"SimpleAdDisplay.useEffect.interval\": ()=>{\n                    setCurrentAdIndex({\n                        \"SimpleAdDisplay.useEffect.interval\": (prev)=>(prev + 1) % ads.length\n                    }[\"SimpleAdDisplay.useEffect.interval\"]);\n                }\n            }[\"SimpleAdDisplay.useEffect.interval\"], 10000);\n            return ({\n                \"SimpleAdDisplay.useEffect\": ()=>clearInterval(interval)\n            })[\"SimpleAdDisplay.useEffect\"];\n        }\n    }[\"SimpleAdDisplay.useEffect\"], [\n        ads.length\n    ]);\n    const handleAdClick = (ad)=>{\n        if (ad.click_url) {\n            window.open(ad.click_url, '_blank', 'noopener,noreferrer');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-200 rounded-lg \".concat(className),\n            style: {\n                height: position === 'header' ? '60px' : position === 'footer' ? '60px' : '120px'\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SimpleAdDisplay.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this);\n    }\n    if (ads.length === 0) {\n        return null;\n    }\n    const currentAd = ads[currentAdIndex];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"simple-ad-display \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cursor-pointer transition-all duration-300 hover:opacity-90\",\n                onClick: ()=>handleAdClick(currentAd),\n                dangerouslySetInnerHTML: {\n                    __html: currentAd.html_content\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SimpleAdDisplay.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, this),\n            ads.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mt-2 space-x-1\",\n                children: ads.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"w-2 h-2 rounded-full transition-colors \".concat(index === currentAdIndex ? 'bg-blue-500' : 'bg-gray-300'),\n                        onClick: ()=>setCurrentAdIndex(index)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SimpleAdDisplay.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SimpleAdDisplay.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\SimpleAdDisplay.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleAdDisplay, \"hHeICSG6yh0QNkilOFucOmNNu+Q=\");\n_c = SimpleAdDisplay;\nvar _c;\n$RefreshReg$(_c, \"SimpleAdDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/SimpleAdDisplay.tsx\n"));

/***/ })

});