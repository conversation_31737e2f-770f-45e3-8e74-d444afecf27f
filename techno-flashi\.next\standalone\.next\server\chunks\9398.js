"use strict";exports.id=9398,exports.ids=[9398],exports.modules={19398:(e,t,n)=>{n.d(t,{Ri:()=>y}),n(25538);var r=n(66437);function o(e){return null!=e&&"object"==typeof e&&!0===e["@@functional/placeholder"]}function u(e){return function t(n){return 0==arguments.length||o(n)?t:e.apply(this,arguments)}}function a(e){return function t(n,r){switch(arguments.length){case 0:return t;case 1:return o(n)?t:u(function(t){return e(n,t)});default:return o(n)&&o(r)?t:o(n)?u(function(t){return e(t,r)}):o(r)?u(function(t){return e(n,t)}):e(n,r)}}}function i(e){return function t(n,r,i){switch(arguments.length){case 0:return t;case 1:return o(n)?t:a(function(t,r){return e(n,t,r)});case 2:return o(n)&&o(r)?t:o(n)?a(function(t,n){return e(t,r,n)}):o(r)?a(function(t,r){return e(n,t,r)}):u(function(t){return e(n,r,t)});default:return o(n)&&o(r)&&o(i)?t:o(n)&&o(r)?a(function(t,n){return e(t,n,i)}):o(n)&&o(i)?a(function(t,n){return e(t,r,n)}):o(r)&&o(i)?a(function(t,r){return e(n,t,r)}):o(n)?u(function(t){return e(t,r,i)}):o(r)?u(function(t){return e(n,t,i)}):o(i)?u(function(t){return e(n,r,t)}):e(n,r,i)}}}function c(e){return"[object Object]"===Object.prototype.toString.call(e)}function s(e,t){return Object.prototype.hasOwnProperty.call(t,e)}var f=i(function(e,t,n){var r,o={};for(r in n=n||{},t=t||{})s(r,t)&&(o[r]=s(r,n)?e(r,t[r],n[r]):t[r]);for(r in n)s(r,n)&&!s(r,o)&&(o[r]=n[r]);return o}),l=i(function e(t,n,r){return f(function(n,r,o){return c(r)&&c(o)?e(t,r,o):t(n,r,o)},n,r)}),p=a(function(e,t){return l(function(e,t,n){return n},e,t)});function h(){return"undefined"!=typeof window&&void 0!==window.document}var d={path:"/",sameSite:"lax",httpOnly:!1,maxAge:31536e6};async function g(e,t){let n=await t(e);if(n)return n;let r=[];for(let n=0;;n++){let o=`${e}.${n}`,u=await t(o);if(!u)break;r.push(u)}if(r.length>0)return r.join("")}async function m(e,t,n){if(await t(e))return void await n(e);for(let r=0;;r++){let o=`${e}.${r}`;if(!await t(o))break;await n(o)}}function y(e,t,n){if(!e||!t)throw Error(`Your project's URL and Key are required to create a Supabase client!

Check your Supabase project's API settings to find these values

https://supabase.com/dashboard/project/_/settings/api`);let{cookies:o,cookieOptions:u,...a}=n;(null==u?void 0:u.name)&&(a.auth={...a.auth,storageKey:u.name});let i=p({global:{headers:{"X-Client-Info":"supabase-ssr/0.1.0"}},auth:{flowType:"pkce",autoRefreshToken:h(),detectSessionInUrl:h(),persistSession:!0,storage:{isServer:!0,getItem:async e=>await g(e,async e=>{if("function"==typeof o.get)return await o.get(e)}),setItem:async(e,t)=>{let n=function(e,t,n){let r=(void 0)??3180,o=encodeURIComponent(t);if(o.length<=r)return[{name:e,value:t}];let u=[];for(;o.length>0;){let e=o.slice(0,r),t=e.lastIndexOf("%");t>r-3&&(e=e.slice(0,t));let n="";for(;e.length>0;)try{n=decodeURIComponent(e);break}catch(t){if(t instanceof URIError&&"%"===e.at(-3)&&e.length>3)e=e.slice(0,e.length-3);else throw t}u.push(n),o=o.slice(e.length)}return u.map((t,n)=>({name:`${e}.${n}`,value:t}))}(e,t);await Promise.all(n.map(async e=>{"function"==typeof o.set&&await o.set(e.name,e.value,{...d,...u,maxAge:d.maxAge})}))},removeItem:async e=>{if("function"==typeof o.remove&&"function"!=typeof o.get)return void console.log("Removing chunked cookie without a `get` method is not supported.\n\n	When you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\n\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client");m(e,async e=>{if("function"==typeof o.get)return await o.get(e)},async e=>{if("function"==typeof o.remove)return await o.remove(e,{...d,...u,maxAge:0})})}}}},a);return(0,r.UU)(e,t,i)}},25538:(e,t)=>{Object.prototype.toString}};