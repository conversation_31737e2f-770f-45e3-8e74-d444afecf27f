{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "OWF8buwxs8ZchOJvx_HGk", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "EOLMuSNB25Lw8T36SdvfzjajKjIsmvgFp+SQnzCGdD4=", "__NEXT_PREVIEW_MODE_ID": "874d6f63ad6d951e9de99391a6b9aaf6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8429e8cc0b06cf843a943ba8af34037e9efa333699b99c9db89cbfddaec6579a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8d0b9050d7f97080b05a27899e074404f2c45e94bac3fd2a955c7e27444ad4d4"}}}, "functions": {}, "sortedMiddleware": ["/"]}