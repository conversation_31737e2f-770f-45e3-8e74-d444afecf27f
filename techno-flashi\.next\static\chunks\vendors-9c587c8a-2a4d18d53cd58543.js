"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2033],{33063:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let l=r(88229),n=r(6966),o=r(95155),a=n._(r(12115)),i=l._(r(47650)),u=l._(r(15564)),s=r(38883),d=r(95840),f=r(86752);r(43230);let c=r(70901),p=l._(r(51193)),m=r(6654),g={deviceSizes:[640,750,828,1080,1200,1920],imageSizes:[16,32,48,64,96,128,256],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1};function h(e,t,r,l,n,o,a){let i=null==e?void 0:e.src;e&&e["data-loaded-src"]!==i&&(e["data-loaded-src"]=i,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let l=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>l,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{l=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==l?void 0:l.current)&&l.current(e)}}))}function y(e){return a.use?{fetchPriority:e}:{fetchpriority:e}}let v=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:l,sizes:n,height:i,width:u,decoding:s,className:d,style:f,fetchPriority:c,placeholder:p,loading:g,unoptimized:v,fill:b,onLoadRef:_,onLoadingCompleteRef:S,setBlurComplete:j,setShowAltText:x,sizesInput:C,onLoad:P,onError:E,...z}=e,A=(0,a.useCallback)(e=>{e&&(E&&(e.src=e.src),e.complete&&h(e,p,_,S,j,v,C))},[r,p,_,S,j,E,v,C]),w=(0,m.useMergedRef)(t,A);return(0,o.jsx)("img",{...z,...y(c),loading:g,width:u,height:i,decoding:s,"data-nimg":b?"fill":"1",className:d,style:f,sizes:n,srcSet:l,src:r,ref:w,onLoad:e=>{h(e.currentTarget,p,_,S,j,v,C)},onError:e=>{x(!0),"empty"!==p&&j(!0),E&&E(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,l={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&i.default.preload?(i.default.preload(r.src,l),null):(0,o.jsx)(u.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...l},"__nimg-"+r.src+r.srcSet+r.sizes)})}let _=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(c.RouterContext),l=(0,a.useContext)(f.ImageConfigContext),n=(0,a.useMemo)(()=>{var e;let t=g||l||d.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:o}},[l]),{onLoad:i,onLoadingComplete:u}=e,m=(0,a.useRef)(i);(0,a.useEffect)(()=>{m.current=i},[i]);let h=(0,a.useRef)(u);(0,a.useEffect)(()=>{h.current=u},[u]);let[y,_]=(0,a.useState)(!1),[S,j]=(0,a.useState)(!1),{props:x,meta:C}=(0,s.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:y,showAltText:S});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v,{...x,unoptimized:C.unoptimized,placeholder:C.placeholder,fill:C.fill,onLoadRef:m,onLoadingCompleteRef:h,setBlurComplete:_,setShowAltText:j,sizesInput:e.sizes,ref:t}),C.priority?(0,o.jsx)(b,{isAppRouter:!r,imgAttributes:x}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84547:(e,t,r)=>{let l;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},isEqualNode:function(){return o}});let n=r(79611);function o(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let l=t.cloneNode(!0);return l.setAttribute("nonce",""),l.nonce=r,r===e.nonce&&e.isEqualNode(l)}}return e.isEqualNode(t)}function a(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"])if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;else e.props.href=e.props["data-href"],e.props["data-href"]=void 0;let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,n="";if(r){let{children:e}=r.props;n="string"==typeof e?e:Array.isArray(e)?e.join(""):""}n!==document.title&&(document.title=n),["meta","base","link","style","script"].forEach(e=>{l(e,t[e]||[])})}}}l=(e,t)=>{let r=document.querySelector("head");if(!r)return;let l=new Set(r.querySelectorAll(""+e+"[data-next-head]"));if("meta"===e){let e=r.querySelector("meta[charset]");null!==e&&l.add(e)}let a=[];for(let e=0;e<t.length;e++){let r=function(e){let{type:t,props:r}=e,l=document.createElement(t);(0,n.setAttributesFromProps)(l,r);let{children:o,dangerouslySetInnerHTML:a}=r;return a?l.innerHTML=a.__html||"":o&&(l.textContent="string"==typeof o?o:Array.isArray(o)?o.join(""):""),l}(t[e]);r.setAttribute("data-next-head","");let i=!0;for(let e of l)if(o(e,r)){l.delete(e),i=!1;break}i&&a.push(r)}for(let e of l){var i;null==(i=e.parentNode)||i.removeChild(e)}for(let e of a)"meta"===e.tagName.toLowerCase()&&null!==e.getAttribute("charset")&&r.prepend(e),r.appendChild(e)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);