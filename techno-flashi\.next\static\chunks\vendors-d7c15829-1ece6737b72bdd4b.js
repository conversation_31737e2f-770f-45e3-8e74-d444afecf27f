"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5138],{214:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let n=r(66361),o=r(70427),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:l}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1025:(e,t,r)=>{function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(16023),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2792:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(64252),o=r(62092),l=r(98069),a=n._(r(71827)),u=r(54591),i=r(49163),f=r(20541),d=r(54902),s=r(37176);r(43802);class c{getPageList(){return(0,s.getClientBuildManifest)().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$",originalSource:"/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:n}=e,{pathname:s,query:c,search:p}=(0,f.parseRelativeUrl)(r),{pathname:_}=(0,f.parseRelativeUrl)(t),b=(0,d.removeTrailingSlash)(s);if("/"!==b[0])throw Object.defineProperty(Error('Route name should start with a "/", got "'+b+'"'),"__NEXT_ERROR_CODE",{value:"E303",enumerable:!1,configurable:!0});var y=e.skipInterpolation?_:(0,i.isDynamicRoute)(b)?(0,l.interpolateAs)(s,_,c).result:b;let h=(0,a.default)((0,d.removeTrailingSlash)((0,u.addLocale)(y,n)),".json");return(0,o.addBasePath)("/_next/data/"+this.buildId+h+p,!0)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=(0,s.createRouteLoader)(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return b},initScriptLoader:function(){return y}});let n=r(64252),o=r(88365),l=r(37876),a=n._(r(98477)),u=o._(r(14232)),i=r(68831),f=r(79611),d=r(16959),s=new Map,c=new Set,p=e=>{if(a.default.preinit)return void e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},_=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:l,children:a="",strategy:u="afterInteractive",onError:i,stylesheets:d}=e,_=r||t;if(_&&c.has(_))return;if(s.has(t)){c.add(_),s.get(t).then(n,i);return}let b=()=>{o&&o(),c.add(_)},y=document.createElement("script"),h=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),n&&n.call(this,t),b()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){i&&i(e)});l?(y.innerHTML=l.__html||"",b()):a?(y.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",b()):t&&(y.src=t,s.set(t,h)),(0,f.setAttributesFromProps)(y,e),"worker"===u&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",u),d&&p(d),document.body.appendChild(y)};function b(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))}):_(e)}function y(e){e.forEach(b),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");c.add(t)})}function h(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:f="afterInteractive",onError:s,stylesheets:p,...b}=e,{updateScripts:y,scripts:h,getIsSsr:g,appDir:m,nonce:v}=(0,u.useContext)(i.HeadManagerContext),O=(0,u.useRef)(!1);(0,u.useEffect)(()=>{let e=t||r;O.current||(o&&e&&c.has(e)&&o(),O.current=!0)},[o,t,r]);let j=(0,u.useRef)(!1);if((0,u.useEffect)(()=>{if(!j.current){if("afterInteractive"===f)_(e);else"lazyOnload"===f&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>_(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))}));j.current=!0}},[e,f]),("beforeInteractive"===f||"worker"===f)&&(y?(h[f]=(h[f]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:s,...b}]),y(h)):g&&g()?c.add(t||r):g&&!g()&&_(e)),m){if(p&&p.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===f)if(!r)return b.dangerouslySetInnerHTML&&(b.children=b.dangerouslySetInnerHTML.__html,delete b.dangerouslySetInnerHTML),(0,l.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...b,id:t}])+")"}});else return a.default.preload(r,b.integrity?{as:"script",integrity:b.integrity,nonce:v,crossOrigin:b.crossOrigin}:{as:"script",nonce:v,crossOrigin:b.crossOrigin}),(0,l.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...b,id:t}])+")"}});"afterInteractive"===f&&r&&a.default.preload(r,b.integrity?{as:"script",integrity:b.integrity,nonce:v,crossOrigin:b.crossOrigin}:{as:"script",nonce:v,crossOrigin:b.crossOrigin})}return null}Object.defineProperty(h,"__nextScript",{value:!0});let g=h;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(12115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=l(e,n)),t&&(o.current=l(t,n))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6698:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"onRecoverableError",{enumerable:!0,get:function(){return i}});let n=r(88229),o=r(45262),l=r(21646),a=r(95128),u=n._(r(15807)),i=(e,t)=>{let r=(0,u.default)(e)&&"cause"in e?e.cause:e,n=(0,a.getReactStitchedError)(r);(0,o.isBailoutToCSRError)(r)||(0,l.reportGlobalError)(n)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16959:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21646:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reportGlobalError",{enumerable:!0,get:function(){return r}});let r="function"==typeof reportError?reportError:e=>{globalThis.console.error(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24609:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(64252)._(r(29871));class o{end(e){if("ended"===this.state.state)throw Object.defineProperty(Error("Span has already ended"),"__NEXT_ERROR_CODE",{value:"E17",enumerable:!1,configurable:!0});this.state={state:"ended",endTime:null!=e?e:Date.now()},this.onSpanEnd(this)}constructor(e,t,r){var n,o;this.name=e,this.attributes=null!=(n=t.attributes)?n:{},this.startTime=null!=(o=t.startTime)?o:Date.now(),this.onSpanEnd=r,this.state={state:"inprogress"}}}class l{startSpan(e,t){return new o(e,t,this.handleSpanEnd)}onSpanEnd(e){return this._emitter.on("spanend",e),()=>{this._emitter.off("spanend",e)}}constructor(){this._emitter=(0,n.default)(),this.handleSpanEnd=e=>{this._emitter.emit("spanend",e)}}}let a=new l;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25842:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(33718),r(67647);let n=r(39525);window.next={version:n.version,get router(){return n.router},emitter:n.emitter},(0,n.initialize)({}).then(()=>(0,n.hydrate)()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},27829:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"makeUntrackedExoticParams",{enumerable:!0,get:function(){return l}});let n=r(7541),o=new WeakMap;function l(e){let t=o.get(e);if(t)return t;let r=Promise.resolve(e);return o.set(e,r),Object.keys(e).forEach(t=>{n.wellKnownProperties.has(t)||(r[t]=e[t])}),r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33558:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRenderParamsFromClient",{enumerable:!0,get:function(){return n}});let n=r(27829).makeUntrackedExoticParams;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33718:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),r(78757),self.__next_set_public_path__=e=>{r.p=e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},36818:(e,t)=>{let r;function n(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return n}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37176:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return y},getClientBuildManifest:function(){return _},isAssetError:function(){return d},markAssetError:function(){return f}}),r(64252),r(71827);let n=r(36818),o=r(16959),l=r(78757),a=r(70536);function u(e,t,r){let n,o=t.get(e);if(o)return"future"in o?o.future:Promise.resolve(o);let l=new Promise(e=>{n=e});return t.set(e,{resolve:n,future:l}),r?r().then(e=>(n(e),e)).catch(r=>{throw t.delete(e),r}):l}let i=Symbol("ASSET_LOAD_ERROR");function f(e){return Object.defineProperty(e,i,{})}function d(e){return e&&i in e}let s=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),c=()=>(0,l.getDeploymentIdQueryOrEmptyString)();function p(e,t,r){return new Promise((n,l)=>{let a=!1;e.then(e=>{a=!0,n(e)}).catch(l),(0,o.requestIdleCallback)(()=>setTimeout(()=>{a||l(r)},t))})}function _(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):p(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,f(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function b(e,t){return _().then(r=>{if(!(t in r))throw f(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let o=r[t].map(t=>e+"/_next/"+(0,a.encodeURIPath)(t));return{scripts:o.filter(e=>e.endsWith(".js")).map(e=>(0,n.__unsafeCreateTrustedScriptURL)(e)+c()),css:o.filter(e=>e.endsWith(".css")).map(e=>e+c())}})}function y(e){let t=new Map,r=new Map,n=new Map,l=new Map;function a(e){{var t;let n=r.get(e.toString());return n?n:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),n=new Promise((r,n)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>n(f(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),n)}}function i(e){let t=n.get(e);return t||n.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw f(e)})),t}return{whenEntrypoint:e=>u(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let n=t.get(e);n&&"resolve"in n?r&&(t.set(e,r),n.resolve(r)):(r?t.set(e,r):t.delete(e),l.delete(e))})},loadRoute(r,n){return u(r,l,()=>{let o;return p(b(e,r).then(e=>{let{scripts:n,css:o}=e;return Promise.all([t.has(r)?[]:Promise.all(n.map(a)),Promise.all(o.map(i))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,f(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,n=Object.assign({styles:r},t);return"error"in t?t:n}).catch(e=>{if(n)throw e;return{error:e}}).finally(()=>null==o?void 0:o())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():b(e,t).then(e=>Promise.all(s?e.scripts.map(e=>{var t,r,n;return t=e.toString(),r="script",new Promise((e,o)=>{let l='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(l))return e();n=document.createElement("link"),r&&(n.as=r),n.rel="prefetch",n.crossOrigin=void 0,n.onload=e,n.onerror=()=>o(f(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),n.href=t,document.head.appendChild(n)})}):[])).then(()=>{(0,o.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41318:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RouteAnnouncer:function(){return i},default:function(){return f}});let n=r(64252),o=r(37876),l=n._(r(14232)),a=r(84294),u={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},i=()=>{let{asPath:e}=(0,a.useRouter)(),[t,r]=l.default.useState(""),n=l.default.useRef(e);return l.default.useEffect(()=>{if(n.current!==e)if(n.current=e,document.title)r(document.title);else{var t;let n=document.querySelector("h1");r((null!=(t=null==n?void 0:n.innerText)?t:null==n?void 0:n.textContent)||e)}},[e]),(0,o.jsx)("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:u,children:t})},f=i;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41921:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return s}});let n=r(78040),o=r(8480),l=r(40990),a=r(2746),u=r(68205),i=r(51533),f=r(63069),d=r(98069);function s(e,t,r){let s,c="string"==typeof t?t:(0,o.formatWithValidation)(t),p=c.match(/^[a-zA-Z]{1,}:\/\//),_=p?c.slice(p[0].length):c;if((_.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+c+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(_);c=(p?p[0]:"")+t}if(!(0,i.isLocalURL)(c))return r?[c]:c;try{s=new URL(c.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){s=new URL("/","http://n")}try{let e=new URL(c,s);e.pathname=(0,u.normalizePathTrailingSlash)(e.pathname);let t="";if((0,f.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:u}=(0,d.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,o.formatWithValidation)({pathname:a,hash:e.hash,query:(0,l.omit)(r,u)}))}let a=e.origin===s.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[c]:c}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42714:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return l}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function l(e,t){for(let[l,a]of Object.entries(t)){if(!t.hasOwnProperty(l)||n.includes(l)||void 0===a)continue;let u=r[l]||l.toLowerCase();"SCRIPT"===e.tagName&&o(u)?e[u]=!!a:e.setAttribute(u,String(a)),(!1===a||"SCRIPT"===e.tagName&&o(u)&&(!a||"false"===a))&&(e.setAttribute(u,""),e.removeAttribute(u))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44882:(e,t,r)=>{function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(87102),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63836:(e,t,r)=>{function n(e,t){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return n}}),r(83670),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},67205:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRenderSearchParamsFromClient",{enumerable:!0,get:function(){return n}});let n=r(88324).makeUntrackedExoticSearchParams;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68205:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return l}});let n=r(54902),o=r(83670),l=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:l}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+l};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69155:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{onCaughtError:function(){return i},onUncaughtError:function(){return f}}),r(95128),r(65444);let n=r(22858),o=r(45262),l=r(21646),a=r(66905),u=r(26614);function i(e,t){var r;let l,i=null==(r=t.errorBoundary)?void 0:r.constructor;if(l=l||i===u.ErrorBoundaryHandler&&t.errorBoundary.props.errorComponent===u.GlobalError)return f(e,t);(0,o.isBailoutToCSRError)(e)||(0,n.isNextRouterError)(e)||(0,a.originConsoleError)(e)}function f(e,t){(0,o.isBailoutToCSRError)(e)||(0,n.isNextRouterError)(e)||(0,l.reportGlobalError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69243:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return b},initScriptLoader:function(){return y}});let n=r(88229),o=r(6966),l=r(95155),a=n._(r(47650)),u=o._(r(12115)),i=r(82830),f=r(42714),d=r(92374),s=new Map,c=new Set,p=e=>{if(a.default.preinit)return void e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},_=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:l,children:a="",strategy:u="afterInteractive",onError:i,stylesheets:d}=e,_=r||t;if(_&&c.has(_))return;if(s.has(t)){c.add(_),s.get(t).then(n,i);return}let b=()=>{o&&o(),c.add(_)},y=document.createElement("script"),h=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),n&&n.call(this,t),b()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){i&&i(e)});l?(y.innerHTML=l.__html||"",b()):a?(y.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",b()):t&&(y.src=t,s.set(t,h)),(0,f.setAttributesFromProps)(y,e),"worker"===u&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",u),d&&p(d),document.body.appendChild(y)};function b(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))}):_(e)}function y(e){e.forEach(b),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");c.add(t)})}function h(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:f="afterInteractive",onError:s,stylesheets:p,...b}=e,{updateScripts:y,scripts:h,getIsSsr:g,appDir:m,nonce:v}=(0,u.useContext)(i.HeadManagerContext),O=(0,u.useRef)(!1);(0,u.useEffect)(()=>{let e=t||r;O.current||(o&&e&&c.has(e)&&o(),O.current=!0)},[o,t,r]);let j=(0,u.useRef)(!1);if((0,u.useEffect)(()=>{if(!j.current){if("afterInteractive"===f)_(e);else"lazyOnload"===f&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>_(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>_(e))}));j.current=!0}},[e,f]),("beforeInteractive"===f||"worker"===f)&&(y?(h[f]=(h[f]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:s,...b}]),y(h)):g&&g()?c.add(t||r):g&&!g()&&_(e)),m){if(p&&p.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===f)if(!r)return b.dangerouslySetInnerHTML&&(b.children=b.dangerouslySetInnerHTML.__html,delete b.dangerouslySetInnerHTML),(0,l.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...b,id:t}])+")"}});else return a.default.preload(r,b.integrity?{as:"script",integrity:b.integrity,nonce:v,crossOrigin:b.crossOrigin}:{as:"script",nonce:v,crossOrigin:b.crossOrigin}),(0,l.jsx)("script",{nonce:v,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...b,id:t}])+")"}});"afterInteractive"===f&&r&&a.default.preload(r,b.integrity?{as:"script",integrity:b.integrity,nonce:v,crossOrigin:b.crossOrigin}:{as:"script",nonce:v,crossOrigin:b.crossOrigin})}return null}Object.defineProperty(h,"__nextScript",{value:!0});let g=h;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77207:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"onRecoverableError",{enumerable:!0,get:function(){return i}});let n=r(64252),o=r(63123),l=r(94569),a=r(83575),u=n._(r(66240)),i=(e,t)=>{let r=(0,u.default)(e)&&"cause"in e?e.cause:e,n=(0,a.getReactStitchedError)(r);(0,o.isBailoutToCSRError)(r)||(0,l.reportGlobalError)(n)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79611:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return l}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function l(e,t){for(let[l,a]of Object.entries(t)){if(!t.hasOwnProperty(l)||n.includes(l)||void 0===a)continue;let u=r[l]||l.toLowerCase();"SCRIPT"===e.tagName&&o(u)?e[u]=!!a:e.setAttribute(u,String(a)),(!1===a||"SCRIPT"===e.tagName&&o(u)&&(!a||"false"===a))&&(e.setAttribute(u,""),e.removeAttribute(u))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84294:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return l.default},createRouter:function(){return b},default:function(){return p},makePublicRouterInstance:function(){return y},useRouter:function(){return _},withRouter:function(){return i.default}});let n=r(64252),o=n._(r(14232)),l=n._(r(68276)),a=r(99948),u=n._(r(66240)),i=n._(r(88147)),f={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},d=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],s=["push","replace","reload","back","prefetch","beforePopState"];function c(){if(!f.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.router}Object.defineProperty(f,"events",{get:()=>l.default.events}),d.forEach(e=>{Object.defineProperty(f,e,{get:()=>c()[e]})}),s.forEach(e=>{f[e]=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return c()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{f.ready(()=>{l.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];let o="on"+e.charAt(0).toUpperCase()+e.substring(1);if(f[o])try{f[o](...r)}catch(e){console.error("Error when running the Router event: "+o),console.error((0,u.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let p=f;function _(){let e=o.default.useContext(a.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function b(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return f.router=new l.default(...t),f.readyCallbacks.forEach(e=>e()),f.readyCallbacks=[],f.router}function y(e){let t={};for(let r of d){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=l.default.events,s.forEach(r=>{t[r]=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return e[r](...n)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85169:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatConsoleArgs:function(){return l},parseConsoleArgs:function(){return a}});let n=r(88229)._(r(15807));function o(e,t){switch(typeof e){case"object":if(null===e)return"null";if(Array.isArray(e)){let r="[";if(t<1)for(let n=0;n<e.length;n++)"["!==r&&(r+=","),Object.prototype.hasOwnProperty.call(e,n)&&(r+=o(e[n],t+1));else r+=e.length>0?"...":"";return r+"]"}{if(e instanceof Error)return e+"";let r=Object.keys(e),n="{";if(t<1)for(let l=0;l<r.length;l++){let a=r[l],u=Object.getOwnPropertyDescriptor(e,"key");if(u&&!u.get&&!u.set){let e=JSON.stringify(a);e!=='"'+a+'"'?n+=e+": ":n+=a+": ",n+=o(u.value,t+1)}}else n+=r.length>0?"...":"";return n+"}"}case"string":return JSON.stringify(e);default:return String(e)}}function l(e){let t,r;"string"==typeof e[0]?(t=e[0],r=1):(t="",r=0);let n="",l=!1;for(let a=0;a<t.length;++a){let u=t[a];if("%"!==u||a===t.length-1||r>=e.length){n+=u;continue}let i=t[++a];switch(i){case"c":n=l?""+n+"]":"["+n,l=!l,r++;break;case"O":case"o":n+=o(e[r++],0);break;case"d":case"i":n+=parseInt(e[r++],10);break;case"f":n+=parseFloat(e[r++]);break;case"s":n+=String(e[r++]);break;default:n+="%"+i}}for(;r<e.length;r++)n+=(r>0?" ":"")+o(e[r],0);return n}function a(e){if(e.length>3&&"string"==typeof e[0]&&e[0].startsWith("%c%s%c ")&&"string"==typeof e[1]&&"string"==typeof e[2]&&"string"==typeof e[3]){let t=e[2],r=e[4];return{environmentName:t.trim(),error:(0,n.default)(r)?r:null}}return{environmentName:null,error:null}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88147:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}}),r(64252);let n=r(37876);r(14232);let o=r(84294);function l(e){function t(t){return(0,n.jsx)(e,{router:(0,o.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88324:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"makeUntrackedExoticSearchParams",{enumerable:!0,get:function(){return l}});let n=r(7541),o=new WeakMap;function l(e){let t=o.get(e);if(t)return t;let r=Promise.resolve(e);return o.set(e,r),Object.keys(e).forEach(t=>{n.wellKnownProperties.has(t)||(r[t]=e[t])}),r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92374:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93090:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Portal",{enumerable:!0,get:function(){return l}});let n=r(14232),o=r(98477),l=e=>{let{children:t,type:r}=e,[l,a]=(0,n.useState)(null);return(0,n.useEffect)(()=>{let e=document.createElement(r);return document.body.appendChild(e),a(e),()=>{document.body.removeChild(e)}},[r]),l?(0,o.createPortal)(t,l):null};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94569:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reportGlobalError",{enumerable:!0,get:function(){return r}});let r="function"==typeof reportError?reportError:e=>{globalThis.console.error(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);