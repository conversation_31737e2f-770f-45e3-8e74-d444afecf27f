var _cloneRegExp =
/*#__PURE__*/
require("./internal/_cloneRegExp.js");

var _curry2 =
/*#__PURE__*/
require("./internal/_curry2.js");

var _isRegExp =
/*#__PURE__*/
require("./internal/_isRegExp.js");

var toString =
/*#__PURE__*/
require("./toString.js");
/**
 * Determines whether a given string matches a given regular expression.
 *
 * @func
 * @memberOf R
 * @since v0.12.0
 * @category String
 * @sig RegExp -> String -> <PERSON>olean
 * @param {RegExp} pattern
 * @param {String} str
 * @return {<PERSON><PERSON>an}
 * @see R.match
 * @example
 *
 *      R.test(/^x/, 'xyz'); //=> true
 *      R.test(/^y/, 'xyz'); //=> false
 */


var test =
/*#__PURE__*/
_curry2(function test(pattern, str) {
  if (!_isRegExp(pattern)) {
    throw new TypeError('‘test’ requires a value of type RegExp as its first argument; received ' + toString(pattern));
  }

  return _cloneRegExp(pattern).test(str);
});

module.exports = test;