(()=>{var e={};e.id=6693,e.ids=[6693],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14125:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>d});var t=r(60687),a=r(43210),i=r(16391),l=r(85814),n=r.n(l);function d(){let[e,s]=(0,a.useState)(!1),[r,l]=(0,a.useState)(null),d=async()=>{s(!0);try{let{data:e,error:s}=await i.ND.from("ads").select("id, title, status, created_at").limit(10),{data:r,error:t}=await i.ND.from("advertisements").select("id, title, is_active, created_at").limit(10);l({success:!0,oldTable:{exists:!s,count:e?.length||0,sample:e||[]},newTable:{exists:!t,count:r?.length||0,sample:r||[]}})}catch(e){l({success:!1,error:e.message})}finally{s(!1)}},o=async()=>{if(confirm("هل أنت متأكد من ترحيل البيانات من الجدول القديم؟")){s(!0);try{let{error:e}=await i.ND.rpc("migrate_ads_to_advertisements");if(e){let{error:e}=await i.ND.from("advertisements").insert([])}alert("تم ترحيل البيانات بنجاح"),await d()}catch(e){alert("خطأ في الترحيل: "+e.message)}finally{s(!1)}}},c=async()=>{if(confirm("⚠️ تحذير: هل أنت متأكد من حذف الجدول القديم؟ هذا الإجراء لا يمكن التراجع عنه!")&&confirm("تأكيد نهائي: سيتم حذف جدول ads القديم نهائياً!")){s(!0);try{await i.ND.rpc("drop_old_ads_table"),alert("تم حذف الجدول القديم بنجاح"),await d()}catch(e){alert("خطأ في حذف الجدول: "+e.message)}finally{s(!1)}}};return(0,t.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"ترحيل بيانات الإعلانات"}),(0,t.jsx)("p",{className:"text-dark-text-secondary",children:"ترحيل البيانات من الجدول القديم (ads) إلى الجدول الجديد (advertisements)"})]}),(0,t.jsx)(n(),{href:"/admin/ads",className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors",children:"العودة للإعلانات"})]}),(0,t.jsxs)("div",{className:"bg-yellow-900 border border-yellow-700 rounded-xl p-6 mb-8",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-yellow-300 mb-4",children:"\uD83D\uDD0D المشكلة المكتشفة"}),(0,t.jsxs)("div",{className:"text-yellow-100 space-y-2",children:[(0,t.jsxs)("p",{children:["• لديك جدولين منفصلين للإعلانات: ",(0,t.jsx)("code",{children:"ads"})," (القديم) و ",(0,t.jsx)("code",{children:"advertisements"})," (الجديد)"]}),(0,t.jsx)("p",{children:"• لوحة التحكم تقرأ من الجدول الجديد بينما بعض المكونات تقرأ من القديم"}),(0,t.jsx)("p",{children:"• هذا يسبب عدم تطابق في البيانات المعروضة"}),(0,t.jsx)("p",{children:"• الرابط الذي أرسلته يشير إلى محرر SQL في Supabase حيث يمكنك رؤية هذه المشكلة"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,t.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"فحص الجداول"}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"فحص حالة الجدولين القديم والجديد"}),(0,t.jsx)("button",{onClick:d,disabled:e,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:e?"جاري الفحص...":"فحص الجداول"})]}),(0,t.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"ترحيل البيانات"}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"ترحيل البيانات المتبقية من القديم للجديد"}),(0,t.jsx)("button",{onClick:o,disabled:e,className:"w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:e?"جاري الترحيل...":"ترحيل البيانات"})]}),(0,t.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"حذف الجدول القديم"}),(0,t.jsx)("p",{className:"text-gray-300 mb-4",children:"حذف الجدول القديم نهائياً (خطر!)"}),(0,t.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:e?"جاري الحذف...":"⚠️ حذف الجدول القديم"})]})]}),r&&(0,t.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:r.success?"✅ نتائج الفحص":"❌ خطأ في الفحص"}),r.success?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-3",children:"\uD83D\uDCCA الجدول القديم (ads)"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,t.jsx)("span",{className:r.oldTable.exists?"text-green-400":"text-red-400",children:r.oldTable.exists?"موجود":"غير موجود"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,t.jsx)("span",{className:"text-white",children:r.oldTable.count})]})]}),r.oldTable.sample.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,t.jsx)("div",{className:"space-y-1",children:r.oldTable.sample.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.status]},e.id))})]})]}),(0,t.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-3",children:"\uD83D\uDCCA الجدول الجديد (advertisements)"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,t.jsx)("span",{className:r.newTable.exists?"text-green-400":"text-red-400",children:r.newTable.exists?"موجود":"غير موجود"})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,t.jsx)("span",{className:"text-white",children:r.newTable.count})]})]}),r.newTable.sample.length>0&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,t.jsx)("div",{className:"space-y-1",children:r.newTable.sample.slice(0,3).map(e=>(0,t.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.is_active?"نشط":"غير نشط"]},e.id))})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-900 border border-blue-700 rounded-lg p-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-300 mb-3",children:"\uD83D\uDCA1 التوصيات"}),(0,t.jsxs)("div",{className:"text-blue-100 space-y-2",children:[r.oldTable.exists&&r.oldTable.count>0&&(0,t.jsx)("p",{children:"• يُنصح بترحيل البيانات المتبقية من الجدول القديم"}),r.oldTable.exists&&0===r.oldTable.count&&(0,t.jsx)("p",{children:"• يمكن حذف الجدول القديم بأمان لأنه فارغ"}),(0,t.jsx)("p",{children:"• تأكد من أن جميع المكونات تستخدم الجدول الجديد (advertisements)"}),(0,t.jsx)("p",{children:"• قم بعمل نسخة احتياطية قبل حذف الجدول القديم"})]})]})]}):(0,t.jsx)("div",{className:"bg-red-900 border border-red-700 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-300",children:r.error})})]}),(0,t.jsxs)("div",{className:"mt-8 bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"معلومات الترحيل"}),(0,t.jsxs)("div",{className:"space-y-3 text-gray-300",children:[(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"الجدول القديم (ads):"})," يستخدم status, placement, description"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"الجدول الجديد (advertisements):"})," يستخدم is_active, position, content"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"الترحيل:"})," يحول البيانات من الهيكل القديم للجديد تلقائياً"]}),(0,t.jsxs)("p",{children:["• ",(0,t.jsx)("strong",{children:"الحذف:"})," يحذف الجدول القديم نهائياً بعد التأكد من الترحيل"]})]})]}),(0,t.jsx)("div",{className:"mt-8 text-center",children:(0,t.jsxs)("div",{className:"space-x-4 space-x-reverse",children:[(0,t.jsx)(n(),{href:"/admin/ads",className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"إدارة الإعلانات"}),(0,t.jsx)(n(),{href:"/admin/ads/sync",className:"inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"مزامنة البيانات"}),(0,t.jsx)("a",{href:"https://supabase.com/dashboard/project/zgktrwpladrkhhemhnni/editor",target:"_blank",rel:"noopener noreferrer",className:"inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"محرر SQL"})]})})]})})})}},17287:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\ads\\\\migrate\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\ads\\migrate\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,s,r)=>{"use strict";r.d(s,{ProtectedRoute:()=>l});var t=r(60687),a=r(63213),i=r(16189);function l({children:e}){let{user:s,loading:r}=(0,a.A)();return((0,i.useRouter)(),r)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,t.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):s?(0,t.jsx)(t.Fragment,{children:e}):null}r(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,s,r)=>{Promise.resolve().then(r.bind(r,67083))},42809:(e,s,r)=>{Promise.resolve().then(r.bind(r,17287))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,s,r)=>{"use strict";r.d(s,{ProtectedRoute:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},75031:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var t=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(s,d);let o={children:["",{children:["admin",{children:["ads",{children:["migrate",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17287)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\ads\\migrate\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(r.bind(r,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\ads\\migrate\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/ads/migrate/page",pathname:"/admin/ads/migrate",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},79257:(e,s,r)=>{Promise.resolve().then(r.bind(r,14125))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97579:(e,s,r)=>{Promise.resolve().then(r.bind(r,20769))},99111:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var t=r(37413),a=r(67083);function i({children:e}){return(0,t.jsx)(a.ProtectedRoute,{children:(0,t.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}r(31240)}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4447,6437,2675,3595],()=>r(75031));module.exports=t})();