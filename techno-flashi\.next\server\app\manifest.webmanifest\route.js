/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/manifest.webmanifest/route";
exports.ids = ["app/manifest.webmanifest/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_metadata_route_loader_filePath_C_3A_5CUsers_5Cismail_5CDownloads_5CNew_20folder_20_4_5Ctechno_flashi_5Csrc_5Capp_5Cmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-metadata-route-loader?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/manifest.webmanifest/route\",\n        pathname: \"/manifest.webmanifest\",\n        filename: \"manifest\",\n        bundlePath: \"app/manifest.webmanifest/route\"\n    },\n    resolvedPagePath: \"next-metadata-route-loader?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\",\n    nextConfigOutput,\n    userland: next_metadata_route_loader_filePath_C_3A_5CUsers_5Cismail_5CDownloads_5CNew_20folder_20_4_5Ctechno_flashi_5Csrc_5Capp_5Cmanifest_ts_isDynamicRouteExtension_1_next_metadata_route___WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__":
/*!*************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__ ***!
  \*************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var C_Users_ismail_Downloads_New_folder_4_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./src/app/manifest.ts */ \"(rsc)/./src/app/manifest.ts\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/webpack/loaders/metadata/resolve-route-data */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/metadata/resolve-route-data.js\");\n/* harmony import */ var next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__);\n/* dynamic asset route */\n\n\n\n\nconst contentType = \"application/manifest+json\"\nconst fileType = \"manifest\"\n\n\n  if (typeof C_Users_ismail_Downloads_New_folder_4_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"] !== 'function') {\n    throw new Error('Default export is missing in \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\manifest.ts\"')\n  }\n  \n\n\nasync function GET() {\n  const data = await (0,C_Users_ismail_Downloads_New_folder_4_techno_flashi_src_app_manifest_ts__WEBPACK_IMPORTED_MODULE_1__[\"default\"])()\n  const content = (0,next_dist_build_webpack_loaders_metadata_resolve_route_data__WEBPACK_IMPORTED_MODULE_2__.resolveRouteData)(data, fileType)\n\n  return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(content, {\n    headers: {\n      'Content-Type': contentType,\n      'Cache-Control': \"public, max-age=0, must-revalidate\",\n    },\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLXJvdXRlLWxvYWRlci5qcz9maWxlUGF0aD1DJTNBJTVDVXNlcnMlNUNpc21haWwlNUNEb3dubG9hZHMlNUNOZXclMjBmb2xkZXIlMjAoNCklNUN0ZWNobm8tZmxhc2hpJTVDc3JjJTVDYXBwJTVDbWFuaWZlc3QudHMmaXNEeW5hbWljUm91dGVFeHRlbnNpb249MSE/X19uZXh0X21ldGFkYXRhX3JvdXRlX18iLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUMwQztBQUM4RDtBQUNWOztBQUU5RjtBQUNBOzs7QUFHQSxhQUFhLCtHQUFPO0FBQ3BCO0FBQ0E7QUFDQTs7O0FBR087QUFDUCxxQkFBcUIsbUhBQU87QUFDNUIsa0JBQWtCLDZHQUFnQjs7QUFFbEMsYUFBYSxxREFBWTtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsR0FBRztBQUNIIiwic291cmNlcyI6WyI/X19uZXh0X21ldGFkYXRhX3JvdXRlX18iXSwic291cmNlc0NvbnRlbnQiOlsiLyogZHluYW1pYyBhc3NldCByb3V0ZSAqL1xuaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInXG5pbXBvcnQgaGFuZGxlciBmcm9tIFwiQzpcXFxcVXNlcnNcXFxcaXNtYWlsXFxcXERvd25sb2Fkc1xcXFxOZXcgZm9sZGVyICg0KVxcXFx0ZWNobm8tZmxhc2hpXFxcXHNyY1xcXFxhcHBcXFxcbWFuaWZlc3QudHNcIlxuaW1wb3J0IHsgcmVzb2x2ZVJvdXRlRGF0YSB9IGZyb20gJ25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbWV0YWRhdGEvcmVzb2x2ZS1yb3V0ZS1kYXRhJ1xuXG5jb25zdCBjb250ZW50VHlwZSA9IFwiYXBwbGljYXRpb24vbWFuaWZlc3QranNvblwiXG5jb25zdCBmaWxlVHlwZSA9IFwibWFuaWZlc3RcIlxuXG5cbiAgaWYgKHR5cGVvZiBoYW5kbGVyICE9PSAnZnVuY3Rpb24nKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCdEZWZhdWx0IGV4cG9ydCBpcyBtaXNzaW5nIGluIFwiQzpcXFxcVXNlcnNcXFxcaXNtYWlsXFxcXERvd25sb2Fkc1xcXFxOZXcgZm9sZGVyICg0KVxcXFx0ZWNobm8tZmxhc2hpXFxcXHNyY1xcXFxhcHBcXFxcbWFuaWZlc3QudHNcIicpXG4gIH1cbiAgXG5cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIEdFVCgpIHtcbiAgY29uc3QgZGF0YSA9IGF3YWl0IGhhbmRsZXIoKVxuICBjb25zdCBjb250ZW50ID0gcmVzb2x2ZVJvdXRlRGF0YShkYXRhLCBmaWxlVHlwZSlcblxuICByZXR1cm4gbmV3IE5leHRSZXNwb25zZShjb250ZW50LCB7XG4gICAgaGVhZGVyczoge1xuICAgICAgJ0NvbnRlbnQtVHlwZSc6IGNvbnRlbnRUeXBlLFxuICAgICAgJ0NhY2hlLUNvbnRyb2wnOiBcInB1YmxpYywgbWF4LWFnZT0wLCBtdXN0LXJldmFsaWRhdGVcIixcbiAgICB9LFxuICB9KVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-route-loader.js?filePath=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp%5Cmanifest.ts&isDynamicRouteExtension=1!?__next_metadata_route__\n");

/***/ }),

/***/ "(rsc)/./src/app/manifest.ts":
/*!*****************************!*\
  !*** ./src/app/manifest.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ manifest)\n/* harmony export */ });\nfunction manifest() {\n    return {\n        name: 'TechnoFlash - بوابتك للمستقبل التقني',\n        short_name: 'TechnoFlash',\n        description: 'منصة ويب متكاملة تقدم مقالات تقنية حصرية، ودليل شامل لأدوات الذكاء الاصطناعي، وخدمات متخصصة',\n        start_url: '/',\n        display: 'standalone',\n        background_color: '#0f172a',\n        theme_color: '#38bdf8',\n        orientation: 'portrait',\n        scope: '/',\n        lang: 'ar',\n        dir: 'rtl',\n        icons: [\n            {\n                src: '/favicon.ico',\n                sizes: '48x48',\n                type: 'image/x-icon'\n            },\n            {\n                src: '/icon-192x192.svg',\n                sizes: '192x192',\n                type: 'image/svg+xml'\n            },\n            {\n                src: '/icon-512x512.svg',\n                sizes: '512x512',\n                type: 'image/svg+xml'\n            }\n        ],\n        categories: [\n            'technology',\n            'education',\n            'productivity'\n        ],\n        shortcuts: [\n            {\n                name: 'المقالات',\n                short_name: 'مقالات',\n                description: 'تصفح أحدث المقالات التقنية',\n                url: '/articles',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            },\n            {\n                name: 'أدوات الذكاء الاصطناعي',\n                short_name: 'أدوات AI',\n                description: 'اكتشف أدوات الذكاء الاصطناعي',\n                url: '/ai-tools',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            },\n            {\n                name: 'الخدمات',\n                short_name: 'خدمات',\n                description: 'تصفح خدماتنا التقنية',\n                url: '/services',\n                icons: [\n                    {\n                        src: '/icon-192x192.svg',\n                        sizes: '192x192'\n                    }\n                ]\n            }\n        ]\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/manifest.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fmanifest.webmanifest%2Froute&page=%2Fmanifest.webmanifest%2Froute&appPaths=&pagePath=private-next-app-dir%2Fmanifest.ts&appDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5CDownloads%5CNew%20folder%20(4)%5Ctechno-flashi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();