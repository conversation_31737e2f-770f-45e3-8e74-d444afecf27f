"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_AdBannerTop_tsx";
exports.ids = ["_ssr_src_components_AdBannerTop_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/AdBannerTop.tsx":
/*!****************************************!*\
  !*** ./src/components/AdBannerTop.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdBannerTop)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AdBannerTop({ placement = \"homepage-top\", className = \"\" }) {\n    const [bannerAd, setBannerAd] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AdBannerTop.useEffect\": ()=>{\n            fetchBannerAd();\n        }\n    }[\"AdBannerTop.useEffect\"], [\n        placement\n    ]);\n    const fetchBannerAd = async ()=>{\n        try {\n            const response = await fetch(`/api/ads?type=banner&placement=${placement}&is_active=true&limit=1`);\n            if (response.ok) {\n                const data = await response.json();\n                if (data.ads && data.ads.length > 0) {\n                    setBannerAd(data.ads[0]);\n                }\n            } else {\n                console.error('Failed to fetch banner ad:', response.status);\n            }\n        } catch (error) {\n            console.error('Error fetching banner ad:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleAdClick = async ()=>{\n        if (!bannerAd) return;\n        // تسجيل النقرة\n        try {\n            await fetch(`/api/ads/${bannerAd.id}/click`, {\n                method: 'POST'\n            });\n        } catch (error) {\n            console.error('Failed to record click:', error);\n        }\n        // فتح الرابط\n        if (bannerAd.link_url) {\n            if (bannerAd.target_blank) {\n                window.open(bannerAd.link_url, '_blank');\n            } else {\n                window.location.href = bannerAd.link_url;\n            }\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `w-full bg-gray-100 animate-pulse ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-24 bg-gray-300 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n            lineNumber: 65,\n            columnNumber: 7\n        }, this);\n    }\n    if (!bannerAd) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `w-full bg-gradient-to-r from-primary/5 to-primary/10 border-b border-primary/20 ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative group cursor-pointer\",\n                    onClick: handleAdClick,\n                    title: bannerAd.description || bannerAd.title,\n                    children: [\n                        bannerAd.image_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        src: bannerAd.image_url,\n                                        alt: bannerAd.title,\n                                        width: bannerAd.width || 728,\n                                        height: bannerAd.height || 90,\n                                        className: \"object-contain max-w-full h-auto\",\n                                        style: {\n                                            filter: 'drop-shadow(0 4px 8px rgba(0,0,0,0.1))'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this) : /* إعلان نصي */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center p-6 bg-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105 border-2 border-primary/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-bold text-gray-900 mb-2\",\n                                    children: bannerAd.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                bannerAd.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: bannerAd.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center text-primary font-semibold\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"اضغط للمزيد\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform duration-300\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M15 19l-7-7 7-7\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 13\n                        }, this),\n                        bannerAd.ad_code && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                dangerouslySetInnerHTML: {\n                                    __html: bannerAd.ad_code\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"sr-only\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"إعلان: \",\n                                        bannerAd.title\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                bannerAd.sponsor_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \" - برعاية \",\n                                        bannerAd.sponsor_name\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\",\n                        children: \"إعلان\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\AdBannerTop.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AdBannerTop.tsx\n");

/***/ })

};
;