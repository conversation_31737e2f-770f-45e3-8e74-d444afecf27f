(()=>{var e={};e.id=3630,e.ids=[3630],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10612:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(60687),r=s(43210),l=s(55488);function n({ad:e,onSave:t,onCancel:s,isOpen:l}){let[n,i]=(0,r.useState)({name:"",description:"",ad_type:"banner",ad_format:"html",network:"",html_content:"",css_content:"",javascript_content:"",image_url:"",video_url:"",click_url:"",position:"header",container_id:"",z_index:1e3,target_pages:["*"],target_devices:["desktop","mobile","tablet"],target_countries:[],start_date:"",end_date:"",schedule_days:[0,1,2,3,4,5,6],schedule_hours:[],enabled:!0,priority:5,max_impressions:void 0,max_clicks:void 0,frequency_cap:void 0,responsive_breakpoints:{mobile:768,tablet:1024},mobile_html:"",tablet_html:"",animation_type:"",animation_duration:300,hover_effects:{},ab_test_group:"",ab_test_weight:100,tags:[]}),[d,o]=(0,r.useState)(!1),[c,u]=(0,r.useState)("basic"),m=async e=>{e.preventDefault(),o(!0);try{let e={...n,start_date:n.start_date&&""!==n.start_date.trim()?n.start_date+"T00:00:00Z":void 0,end_date:n.end_date&&""!==n.end_date.trim()?n.end_date+"T23:59:59Z":void 0,max_impressions:n.max_impressions||void 0,max_clicks:n.max_clicks||void 0,frequency_cap:n.frequency_cap||void 0,name:n.name.trim(),description:n.description?.trim()||""};if(!e.name)return void alert("❌ Ad name is required");if(!e.ad_type)return void alert("❌ Ad type is required");if(!e.position)return void alert("❌ Ad position is required");await t(e)}catch(e){if(console.error("Error saving ad:",e),e&&"object"==typeof e&&"message"in e){let t=e.message;t.includes("timestamp")?alert("❌ Invalid date format. Please check your start and end dates."):t.includes("duplicate")?alert("❌ An ad with this name already exists. Please choose a different name."):alert(`❌ Error saving ad: ${t}`)}else alert("❌ Error saving ad. Please try again.")}finally{o(!1)}},x=(e,t)=>{i(s=>({...s,[e]:t}))};return l?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-blue-600 text-white p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:e?"✏️ Edit Advertisement":"➕ Create New Advertisement"}),(0,a.jsx)("button",{onClick:s,className:"text-white hover:text-gray-200 text-2xl",children:"✕"})]})}),(0,a.jsx)("div",{className:"border-b bg-gray-50",children:(0,a.jsx)("nav",{className:"flex space-x-8 px-6",children:[{id:"basic",label:"\uD83D\uDCCB Basic Info",icon:"\uD83D\uDCCB"},{id:"content",label:"\uD83C\uDFA8 Content",icon:"\uD83C\uDFA8"},{id:"targeting",label:"\uD83C\uDFAF Targeting",icon:"\uD83C\uDFAF"},{id:"advanced",label:"⚙️ Advanced",icon:"⚙️"}].map(e=>(0,a.jsx)("button",{onClick:()=>u(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm ${c===e.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:e.label},e.id))})}),(0,a.jsxs)("form",{onSubmit:m,className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:["basic"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCDD Ad Name *"}),(0,a.jsx)("input",{type:"text",value:n.name,onChange:e=>x("name",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Enter ad name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDF10 Network"}),(0,a.jsxs)("select",{value:n.network,onChange:e=>x("network",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"",children:"Select Network"}),(0,a.jsx)("option",{value:"monetag",children:"Monetag"}),(0,a.jsx)("option",{value:"adsense",children:"Google AdSense"}),(0,a.jsx)("option",{value:"custom",children:"Custom"}),(0,a.jsx)("option",{value:"affiliate",children:"Affiliate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFAF Ad Type *"}),(0,a.jsxs)("select",{value:n.ad_type,onChange:e=>x("ad_type",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"banner",children:"Banner"}),(0,a.jsx)("option",{value:"video",children:"Video"}),(0,a.jsx)("option",{value:"interactive",children:"Interactive"}),(0,a.jsx)("option",{value:"text",children:"Text"}),(0,a.jsx)("option",{value:"native",children:"Native"}),(0,a.jsx)("option",{value:"popup",children:"Popup"}),(0,a.jsx)("option",{value:"interstitial",children:"Interstitial"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCD Position *"}),(0,a.jsxs)("select",{value:n.position,onChange:e=>x("position",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"header",children:"Header"}),(0,a.jsx)("option",{value:"sidebar",children:"Sidebar"}),(0,a.jsx)("option",{value:"footer",children:"Footer"}),(0,a.jsx)("option",{value:"in-content",children:"In-Content"}),(0,a.jsx)("option",{value:"popup",children:"Popup"}),(0,a.jsx)("option",{value:"floating",children:"Floating"}),(0,a.jsx)("option",{value:"sticky",children:"Sticky"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"⭐ Priority (1-10)"}),(0,a.jsx)("input",{type:"number",min:"1",max:"10",value:n.priority,onChange:e=>x("priority",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"enabled",checked:n.enabled,onChange:e=>x("enabled",e.target.checked),className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"enabled",className:"text-sm font-medium text-gray-700",children:"✅ Enable Ad"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCDD Description"}),(0,a.jsx)("textarea",{value:n.description,onChange:e=>x("description",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",rows:3,placeholder:"Enter ad description"})]})]}),"content"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCBB HTML Content"}),(0,a.jsx)("textarea",{value:n.html_content,onChange:e=>x("html_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:8,placeholder:"Enter HTML content..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFA8 CSS Content"}),(0,a.jsx)("textarea",{value:n.css_content,onChange:e=>x("css_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Enter CSS styles..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"⚡ JavaScript Content"}),(0,a.jsx)("textarea",{value:n.javascript_content,onChange:e=>x("javascript_content",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm focus:ring-2 focus:ring-blue-500",rows:6,placeholder:"Enter JavaScript code..."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDDBC️ Image URL"}),(0,a.jsx)("input",{type:"url",value:n.image_url,onChange:e=>x("image_url",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/image.jpg"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDD17 Click URL"}),(0,a.jsx)("input",{type:"url",value:n.click_url,onChange:e=>x("click_url",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"https://example.com/landing-page"})]})]})]}),"targeting"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC4 Target Pages"}),(0,a.jsx)("input",{type:"text",value:n.target_pages.join(", "),onChange:e=>x("target_pages",e.target.value.split(",").map(e=>e.trim()).filter(e=>e)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"*, /articles, /ai-tools"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Use * for all pages, or specify paths separated by commas"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCF1 Target Devices"}),(0,a.jsx)("div",{className:"flex space-x-4",children:["desktop","mobile","tablet"].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",checked:n.target_devices.includes(e),onChange:t=>{x("target_devices",t.target.checked?[...n.target_devices,e]:n.target_devices.filter(t=>t!==e))},className:"mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),(0,a.jsx)("span",{className:"text-sm text-gray-700 capitalize",children:e})]},e))})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC5 Start Date"}),(0,a.jsx)("input",{type:"date",value:n.start_date,onChange:e=>x("start_date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCC5 End Date"}),(0,a.jsx)("input",{type:"date",value:n.end_date,onChange:e=>x("end_date",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]})]})]}),"advanced"===c&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDCCA Max Impressions"}),(0,a.jsx)("input",{type:"number",value:n.max_impressions||"",onChange:e=>x("max_impressions",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Unlimited"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDDB1️ Max Clicks"}),(0,a.jsx)("input",{type:"number",value:n.max_clicks||"",onChange:e=>x("max_clicks",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Unlimited"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83D\uDD04 Frequency Cap"}),(0,a.jsx)("input",{type:"number",value:n.frequency_cap||"",onChange:e=>x("frequency_cap",e.target.value?parseInt(e.target.value):void 0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"Per day"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"\uD83C\uDFF7️ Tags"}),(0,a.jsx)("input",{type:"text",value:n.tags.join(", "),onChange:e=>x("tags",e.target.value.split(",").map(e=>e.trim()).filter(e=>e)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",placeholder:"monetag, banner, test"})]})]})]}),(0,a.jsxs)("div",{className:"border-t bg-gray-50 px-6 py-4 flex justify-end space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:s,className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",disabled:d,children:"❌ Cancel"}),(0,a.jsx)("button",{type:"submit",className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",disabled:d,children:d?"⏳ Saving...":e?"\uD83D\uDCBE Update Ad":"➕ Create Ad"})]})]})]})}):null}function i(){let[e,t]=(0,r.useState)("dashboard"),[s,i]=(0,r.useState)([]),[d,o]=(0,r.useState)([]),[c,u]=(0,r.useState)(!0),[m,x]=(0,r.useState)(null),[p,h]=(0,r.useState)(!1),[g,b]=(0,r.useState)(""),[v,f]=(0,r.useState)("all"),[y,j]=(0,r.useState)("all"),[N,_]=(0,r.useState)("all"),w=s.filter(e=>{let t=e.name.toLowerCase().includes(g.toLowerCase())||(e.description||"").toLowerCase().includes(g.toLowerCase()),s="all"===v||e.network===v,a="all"===y||e.position===y,r="all"===N||"enabled"===N&&e.enabled||"disabled"===N&&!e.enabled;return t&&s&&a&&r}),D=async e=>{try{let t=await (0,l.Am)(e);t?(i(e=>[...e,t]),h(!1),alert("✅ Ad created successfully!")):alert("❌ Failed to create ad. Please try again.")}catch(e){console.error("Error creating ad:",e),alert("❌ Error creating ad. Please check the console for details.")}},C=async e=>{if(m)try{let t=await (0,l.dT)(m.id,e);t?(i(e=>e.map(e=>e.id===m.id?t:e)),x(null),alert("✅ Ad updated successfully!")):alert("❌ Failed to update ad. Please try again.")}catch(e){console.error("Error updating ad:",e),alert("❌ Error updating ad. Please check the console for details.")}},k=async e=>{confirm("Are you sure you want to delete this ad?")&&await (0,l.nt)(e)&&i(t=>t.filter(t=>t.id!==e))},A=async e=>{try{let t=await (0,l.dT)(e.id,{enabled:!e.enabled});t&&i(s=>s.map(s=>s.id===e.id?t:s))}catch(e){console.error("Error toggling ad:",e),alert("❌ Error toggling ad status.")}},q={totalAds:s.length,activeAds:s.filter(e=>e.enabled).length,inactiveAds:s.filter(e=>!e.enabled).length,networks:Array.from(new Set(s.map(e=>e.network).filter(Boolean))).length,positions:Array.from(new Set(s.map(e=>e.position))).length,templates:d.length};return c?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"Loading Advanced Advertising Dashboard..."})]})})}):(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("div",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"\uD83C\uDFAF Advanced Advertising Dashboard"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:"Comprehensive advertising management system with multi-network support"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{onClick:()=>h(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 font-medium",children:"➕ Create New Ad"}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700",children:"\uD83D\uDD04 Refresh"})]})]})})}),(0,a.jsx)("div",{className:"bg-white border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"dashboard",label:"\uD83D\uDCCA Dashboard",icon:"\uD83D\uDCCA"},{id:"ads",label:`🎯 Ads (${q.totalAds})`,icon:"\uD83C\uDFAF"},{id:"templates",label:`📋 Templates (${q.templates})`,icon:"\uD83D\uDCCB"},{id:"analytics",label:"\uD83D\uDCC8 Analytics",icon:"\uD83D\uDCC8"},{id:"settings",label:"⚙️ Settings",icon:"⚙️"}].map(s=>(0,a.jsx)("button",{onClick:()=>t(s.id),className:`py-4 px-1 border-b-2 font-medium text-sm ${e===s.id?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:s.label},s.id))})})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:["dashboard"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-blue-600 font-semibold",children:"\uD83C\uDFAF"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Total Ads"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:q.totalAds})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-green-600 font-semibold",children:"✅"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Active Ads"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:q.activeAds})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-yellow-600 font-semibold",children:"\uD83C\uDF10"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Networks"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:q.networks})]})]})}),(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-purple-600 font-semibold",children:"\uD83D\uDCCB"})})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Templates"}),(0,a.jsx)("p",{className:"text-2xl font-semibold text-gray-900",children:q.templates})]})]})})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDE80 Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsx)("button",{onClick:()=>h(!0),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"➕"}),(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Create New Ad"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Start from scratch"})]})}),(0,a.jsx)("button",{onClick:()=>t("templates"),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCCB"}),(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"Use Template"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Quick setup"})]})}),(0,a.jsx)("button",{onClick:()=>t("analytics"),className:"p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDCC8"}),(0,a.jsx)("div",{className:"font-medium text-gray-900",children:"View Analytics"}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:"Performance data"})]})})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCCB Recent Ads"}),(0,a.jsx)("div",{className:"space-y-3",children:s.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${e.enabled?"bg-green-500":"bg-red-500"}`}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[e.network," • ",e.position]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>A(e),className:`px-3 py-1 rounded text-xs font-medium ${e.enabled?"bg-yellow-100 text-yellow-800 hover:bg-yellow-200":"bg-green-100 text-green-800 hover:bg-green-200"}`,children:e.enabled?"⏸️ Pause":"▶️ Enable"}),(0,a.jsx)("button",{onClick:()=>{x(e),h(!0)},className:"px-3 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium hover:bg-blue-200",children:"✏️ Edit"})]})]},e.id))})]})]}),"ads"===e&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white p-6 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Search"}),(0,a.jsx)("input",{type:"text",value:g,onChange:e=>b(e.target.value),placeholder:"Search ads...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Network"}),(0,a.jsxs)("select",{value:v,onChange:e=>f(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Networks"}),(0,a.jsx)("option",{value:"monetag",children:"Monetag"}),(0,a.jsx)("option",{value:"adsense",children:"Google AdSense"}),(0,a.jsx)("option",{value:"custom",children:"Custom"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Position"}),(0,a.jsxs)("select",{value:y,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Positions"}),(0,a.jsx)("option",{value:"header",children:"Header"}),(0,a.jsx)("option",{value:"sidebar",children:"Sidebar"}),(0,a.jsx)("option",{value:"footer",children:"Footer"}),(0,a.jsx)("option",{value:"in-content",children:"In-Content"}),(0,a.jsx)("option",{value:"floating",children:"Floating"}),(0,a.jsx)("option",{value:"popup",children:"Popup"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Status"}),(0,a.jsxs)("select",{value:N,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"enabled",children:"Enabled"}),(0,a.jsx)("option",{value:"disabled",children:"Disabled"})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("h3",{className:"text-lg font-medium text-gray-900",children:["Ads (",w.length,")"]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:w.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:`w-3 h-3 rounded-full ${e.enabled?"bg-green-500":"bg-red-500"}`}),(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,a.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${e.enabled?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.enabled?"✅ Active":"❌ Inactive"})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:e.description}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4 text-sm text-gray-500",children:[(0,a.jsxs)("span",{children:["\uD83C\uDF10 ",e.network||"Custom"]}),(0,a.jsxs)("span",{children:["\uD83D\uDCCD ",e.position]}),(0,a.jsxs)("span",{children:["\uD83C\uDFAF ",e.ad_type]}),(0,a.jsxs)("span",{children:["⭐ Priority: ",e.priority]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("button",{onClick:()=>A(e),className:`px-3 py-1 rounded text-sm font-medium ${e.enabled?"bg-yellow-100 text-yellow-800 hover:bg-yellow-200":"bg-green-100 text-green-800 hover:bg-green-200"}`,children:e.enabled?"⏸️ Pause":"▶️ Enable"}),(0,a.jsx)("button",{onClick:()=>{x(e),h(!0)},className:"px-3 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium hover:bg-blue-200",children:"✏️ Edit"}),(0,a.jsx)("button",{onClick:()=>k(e.id),className:"px-3 py-1 bg-red-100 text-red-800 rounded text-sm font-medium hover:bg-red-200",children:"\uD83D\uDDD1️ Delete"})]})]})},e.id))})]})]}),"templates"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCCB Ad Templates"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:d.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium",children:e.category})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[(0,a.jsxs)("span",{children:["\uD83C\uDF10 ",e.network||"Custom"]}),(0,a.jsxs)("span",{children:["\uD83D\uDCCA Used ",e.usage_count," times"]})]}),(0,a.jsx)("button",{onClick:()=>{},className:"w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 font-medium",children:"\uD83D\uDE80 Use Template"})]},e.id))})]})}),"analytics"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"\uD83D\uDCC8 Analytics Dashboard"}),(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCCA"}),(0,a.jsx)("h4",{className:"text-xl font-medium text-gray-900 mb-2",children:"Analytics Coming Soon"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Comprehensive analytics and performance tracking will be available here."})]})]})}),"settings"===e&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"⚙️ System Settings"}),(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"⚙️"}),(0,a.jsx)("h4",{className:"text-xl font-medium text-gray-900 mb-2",children:"Settings Panel"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Global advertising settings and configuration options will be available here."})]})]})})]}),(0,a.jsx)(n,{ad:m,onSave:m?C:D,onCancel:()=>{h(!1),x(null)},isOpen:p||null!==m})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13554:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\advanced-ads\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\advanced-ads\\page.tsx","default")},18893:(e,t,s)=>{Promise.resolve().then(s.bind(s,13554))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,t,s)=>{"use strict";s.d(t,{ProtectedRoute:()=>n});var a=s(60687),r=s(63213),l=s(16189);function n({children:e}){let{user:t,loading:s}=(0,r.A)();return((0,l.useRouter)(),s)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,a.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):t?(0,a.jsx)(a.Fragment,{children:e}):null}s(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37331:(e,t,s)=>{Promise.resolve().then(s.bind(s,67083))},55488:(e,t,s)=>{"use strict";s.d(t,{Am:()=>l,H1:()=>d,JY:()=>r,dT:()=>n,nt:()=>i,o:()=>o});var a=s(16391);async function r(e){try{let t=a.ND.from("ads").select("*").order("priority",{ascending:!1});e?.enabled!==void 0&&(t=t.eq("enabled",e.enabled)),e?.position&&(t=t.eq("position",e.position)),e?.network&&(t=t.eq("network",e.network)),e?.ad_type&&(t=t.eq("ad_type",e.ad_type));let{data:s,error:r}=await t;if(r)return console.error("Error fetching advanced ads:",r),[];let l=s||[];e?.target_page&&(l=l.filter(t=>{let s=t.target_pages||[];return s.includes("*")||s.some(t=>!!(t===e.target_page||t.endsWith("*")&&e.target_page?.startsWith(t.slice(0,-1))))}));let n=new Date,i=n.getDay(),d=n.getHours();return l=l.filter(e=>!(e.start_date&&new Date(e.start_date)>n||e.end_date&&new Date(e.end_date)<n)&&(!e.schedule_days||!(e.schedule_days.length>0)||!!e.schedule_days.includes(i))&&(!e.schedule_hours||!(e.schedule_hours.length>0)||!!e.schedule_hours.includes(d)))}catch(e){return console.error("Error in getAdvancedAds:",e),[]}}async function l(e){try{let t={...e,start_date:e.start_date&&""!==e.start_date.trim()?e.start_date:null,end_date:e.end_date&&""!==e.end_date.trim()?e.end_date:null,max_impressions:e.max_impressions||null,max_clicks:e.max_clicks||null,frequency_cap:e.frequency_cap||null,target_pages:Array.isArray(e.target_pages)?e.target_pages:["*"],target_devices:Array.isArray(e.target_devices)?e.target_devices:["desktop","mobile","tablet"],target_countries:Array.isArray(e.target_countries)?e.target_countries:[],schedule_days:Array.isArray(e.schedule_days)?e.schedule_days:[0,1,2,3,4,5,6],schedule_hours:Array.isArray(e.schedule_hours)?e.schedule_hours:[],tags:Array.isArray(e.tags)?e.tags:[]},{data:s,error:r}=await a.ND.from("ads").insert([t]).select().single();if(r)return console.error("Error creating advanced ad:",r),null;return s}catch(e){return console.error("Error in createAdvancedAd:",e),null}}async function n(e,t){try{let s={...t,start_date:void 0!==t.start_date?t.start_date&&""!==t.start_date.trim()?t.start_date:null:void 0,end_date:void 0!==t.end_date?t.end_date&&""!==t.end_date.trim()?t.end_date:null:void 0,max_impressions:void 0!==t.max_impressions?t.max_impressions||null:void 0,max_clicks:void 0!==t.max_clicks?t.max_clicks||null:void 0,frequency_cap:void 0!==t.frequency_cap?t.frequency_cap||null:void 0,updated_at:new Date().toISOString()},{data:r,error:l}=await a.ND.from("ads").update(s).eq("id",e).select().single();if(l)return console.error("Error updating advanced ad:",l),null;return r}catch(e){return console.error("Error in updateAdvancedAd:",e),null}}async function i(e){try{let{error:t}=await a.ND.from("ads").delete().eq("id",e);if(t)return console.error("Error deleting advanced ad:",t),!1;return!0}catch(e){return console.error("Error in deleteAdvancedAd:",e),!1}}async function d(e,t){try{let s=a.ND.from("ad_templates").select("*").eq("is_public",!0).order("usage_count",{ascending:!1});e&&(s=s.eq("category",e)),t&&(s=s.eq("network",t));let{data:r,error:l}=await s;if(l)return console.error("Error fetching ad templates:",l),[];return r||[]}catch(e){return console.error("Error in getAdTemplates:",e),[]}}async function o(e,t){try{let{data:s,error:r}=await a.ND.from("ad_templates").select("*").eq("id",e).single();if(r||!s)return console.error("Error fetching template:",r),null;let n=s.html_template||"",i=s.css_template||"",d=s.javascript_template||"";Object.entries(t).forEach(([e,t])=>{let s=`{{${e}}}`;n=n.replace(RegExp(s,"g"),String(t)),i=i.replace(RegExp(s,"g"),String(t)),d=d.replace(RegExp(s,"g"),String(t))});let o={name:t.name||s.name,description:`Created from template: ${s.name}`,ad_type:s.category||"banner",ad_format:"html",network:s.network,html_content:n,css_content:i,javascript_content:d,position:t.position||"header",z_index:t.z_index||1e3,target_pages:t.target_pages||["*"],target_devices:["desktop","mobile","tablet"],target_countries:[],schedule_days:[0,1,2,3,4,5,6],schedule_hours:[],enabled:!0,priority:t.priority||5,responsive_breakpoints:{mobile:768,tablet:1024},animation_duration:300,hover_effects:{},ab_test_weight:100,tags:[s.category,s.network].filter(Boolean)},c=await l(o);return c&&await a.ND.from("ad_templates").update({usage_count:s.usage_count+1}).eq("id",e),c}catch(e){return console.error("Error in createAdFromTemplate:",e),null}}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,t,s)=>{"use strict";s.d(t,{ProtectedRoute:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82509:(e,t,s)=>{Promise.resolve().then(s.bind(s,10612))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95275:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),l=s(88170),n=s.n(l),i=s(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["advanced-ads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,13554)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\advanced-ads\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\advanced-ads\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/advanced-ads/page",pathname:"/admin/advanced-ads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},97579:(e,t,s)=>{Promise.resolve().then(s.bind(s,20769))},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(37413),r=s(67083);function l({children:e}){return(0,a.jsx)(r.ProtectedRoute,{children:(0,a.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}s(31240)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4447,6437,2675,3595],()=>s(95275));module.exports=a})();