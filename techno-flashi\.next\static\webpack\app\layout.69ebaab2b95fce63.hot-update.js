"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9d4ff9714afe\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGlzbWFpbFxcRG93bmxvYWRzXFxOZXcgZm9sZGVyICg0KVxcdGVjaG5vLWZsYXNoaVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOWQ0ZmY5NzE0YWZlXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/InteractiveEffects.tsx":
/*!***********************************************!*\
  !*** ./src/components/InteractiveEffects.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ButtonHoverEffect: () => (/* binding */ ButtonHoverEffect),\n/* harmony export */   InteractiveEffects: () => (/* binding */ InteractiveEffects),\n/* harmony export */   TextGlowEffect: () => (/* binding */ TextGlowEffect),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ InteractiveEffects,ButtonHoverEffect,TextGlowEffect,default auto */ \nvar _s = $RefreshSig$();\n\nfunction InteractiveEffects(param) {\n    let { target, className = '' } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InteractiveEffects.useEffect\": ()=>{\n            const container = containerRef.current;\n            if (!container) return;\n            // إنشاء جسيمات خفيفة\n            const createParticles = {\n                \"InteractiveEffects.useEffect.createParticles\": ()=>{\n                    const particleCount = target === 'header' ? 3 : 5;\n                    for(let i = 0; i < particleCount; i++){\n                        const particle = document.createElement('div');\n                        particle.className = \"absolute pointer-events-none transition-all duration-1000 ease-out \".concat(target === 'header' ? 'w-1 h-1 bg-purple-400/20 rounded-full' : 'w-2 h-2 bg-gradient-to-r from-purple-400/30 to-pink-400/30 rounded-full');\n                        // موضع عشوائي\n                        particle.style.left = Math.random() * 100 + '%';\n                        particle.style.top = Math.random() * 100 + '%';\n                        particle.style.opacity = '0';\n                        container.appendChild(particle);\n                        particlesRef.current.push(particle);\n                    }\n                }\n            }[\"InteractiveEffects.useEffect.createParticles\"];\n            // تحريك الجسيمات مع الماوس\n            const handleMouseMove = {\n                \"InteractiveEffects.useEffect.handleMouseMove\": (e)=>{\n                    const rect = container.getBoundingClientRect();\n                    const x = (e.clientX - rect.left) / rect.width * 100;\n                    const y = (e.clientY - rect.top) / rect.height * 100;\n                    particlesRef.current.forEach({\n                        \"InteractiveEffects.useEffect.handleMouseMove\": (particle, index)=>{\n                            const delay = index * 50;\n                            const offsetX = (Math.random() - 0.5) * 20;\n                            const offsetY = (Math.random() - 0.5) * 20;\n                            setTimeout({\n                                \"InteractiveEffects.useEffect.handleMouseMove\": ()=>{\n                                    particle.style.left = Math.max(0, Math.min(100, x + offsetX)) + '%';\n                                    particle.style.top = Math.max(0, Math.min(100, y + offsetY)) + '%';\n                                    particle.style.opacity = '1';\n                                }\n                            }[\"InteractiveEffects.useEffect.handleMouseMove\"], delay);\n                        }\n                    }[\"InteractiveEffects.useEffect.handleMouseMove\"]);\n                }\n            }[\"InteractiveEffects.useEffect.handleMouseMove\"];\n            // إخفاء الجسيمات عند مغادرة الماوس\n            const handleMouseLeave = {\n                \"InteractiveEffects.useEffect.handleMouseLeave\": ()=>{\n                    particlesRef.current.forEach({\n                        \"InteractiveEffects.useEffect.handleMouseLeave\": (particle, index)=>{\n                            setTimeout({\n                                \"InteractiveEffects.useEffect.handleMouseLeave\": ()=>{\n                                    particle.style.opacity = '0';\n                                }\n                            }[\"InteractiveEffects.useEffect.handleMouseLeave\"], index * 30);\n                        }\n                    }[\"InteractiveEffects.useEffect.handleMouseLeave\"]);\n                }\n            }[\"InteractiveEffects.useEffect.handleMouseLeave\"];\n            // تأثير الموجات عند النقر\n            const handleClick = {\n                \"InteractiveEffects.useEffect.handleClick\": (e)=>{\n                    const rect = container.getBoundingClientRect();\n                    const x = e.clientX - rect.left;\n                    const y = e.clientY - rect.top;\n                    const ripple = document.createElement('div');\n                    ripple.className = \"absolute pointer-events-none rounded-full \".concat(target === 'header' ? 'bg-purple-400/10 border border-purple-400/20' : 'bg-gradient-to-r from-purple-400/10 to-pink-400/10 border border-purple-400/30');\n                    ripple.style.left = x + 'px';\n                    ripple.style.top = y + 'px';\n                    ripple.style.width = '0px';\n                    ripple.style.height = '0px';\n                    ripple.style.transform = 'translate(-50%, -50%)';\n                    container.appendChild(ripple);\n                    // تحريك الموجة\n                    requestAnimationFrame({\n                        \"InteractiveEffects.useEffect.handleClick\": ()=>{\n                            ripple.style.width = '100px';\n                            ripple.style.height = '100px';\n                            ripple.style.opacity = '0';\n                            ripple.style.transition = 'all 0.6s ease-out';\n                        }\n                    }[\"InteractiveEffects.useEffect.handleClick\"]);\n                    // إزالة الموجة\n                    setTimeout({\n                        \"InteractiveEffects.useEffect.handleClick\": ()=>{\n                            container.removeChild(ripple);\n                        }\n                    }[\"InteractiveEffects.useEffect.handleClick\"], 600);\n                }\n            }[\"InteractiveEffects.useEffect.handleClick\"];\n            createParticles();\n            container.addEventListener('mousemove', handleMouseMove);\n            container.addEventListener('mouseleave', handleMouseLeave);\n            container.addEventListener('click', handleClick);\n            return ({\n                \"InteractiveEffects.useEffect\": ()=>{\n                    container.removeEventListener('mousemove', handleMouseMove);\n                    container.removeEventListener('mouseleave', handleMouseLeave);\n                    container.removeEventListener('click', handleClick);\n                    // تنظيف الجسيمات\n                    particlesRef.current.forEach({\n                        \"InteractiveEffects.useEffect\": (particle)=>{\n                            if (particle.parentNode) {\n                                particle.parentNode.removeChild(particle);\n                            }\n                        }\n                    }[\"InteractiveEffects.useEffect\"]);\n                    particlesRef.current = [];\n                }\n            })[\"InteractiveEffects.useEffect\"];\n        }\n    }[\"InteractiveEffects.useEffect\"], [\n        target\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"absolute inset-0 overflow-hidden \".concat(className),\n        style: {\n            zIndex: target === 'header' ? 1 : 0\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(InteractiveEffects, \"ESYFMR/CdU1IiPTv+5p3KhFrfV4=\");\n_c = InteractiveEffects;\n// مكون تأثيرات الأزرار\nfunction ButtonHoverEffect(param) {\n    let { children, className = '', variant = 'primary' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative overflow-hidden \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 transition-all duration-300 ease-out transform scale-x-0 group-hover:scale-x-100 origin-left \".concat(variant === 'primary' ? 'bg-gradient-to-r from-purple-700 to-pink-700' : 'bg-purple-400/10')\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 w-full h-full \".concat(variant === 'primary' ? 'bg-gradient-to-r from-transparent via-white/10 to-transparent' : 'bg-gradient-to-r from-transparent via-purple-400/20 to-transparent', \" transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-out\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n        lineNumber: 139,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ButtonHoverEffect;\n// مكون تأثيرات النص\nfunction TextGlowEffect(param) {\n    let { children, className = '' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group relative \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-purple-600/20 via-pink-600/20 to-purple-600/20 blur-xl\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 transition-all duration-300 group-hover:scale-105\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\InteractiveEffects.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_c2 = TextGlowEffect;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InteractiveEffects);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"InteractiveEffects\");\n$RefreshReg$(_c1, \"ButtonHoverEffect\");\n$RefreshReg$(_c2, \"TextGlowEffect\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InteractiveEffects.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProfessionalHeader.tsx":
/*!***********************************************!*\
  !*** ./src/components/ProfessionalHeader.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProfessionalHeader: () => (/* binding */ ProfessionalHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_InteractiveEffects__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/InteractiveEffects */ \"(app-pages-browser)/./src/components/InteractiveEffects.tsx\");\n/* __next_internal_client_entry_do_not_use__ ProfessionalHeader auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProfessionalHeader() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { user, signOut, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProfessionalHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProfessionalHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 20);\n                }\n            }[\"ProfessionalHeader.useEffect.handleScroll\"];\n            const handleResize = {\n                \"ProfessionalHeader.useEffect.handleResize\": ()=>{\n                    if (window.innerWidth >= 1024) {\n                        setIsMenuOpen(false);\n                    }\n                }\n            }[\"ProfessionalHeader.useEffect.handleResize\"];\n            window.addEventListener('scroll', handleScroll);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"ProfessionalHeader.useEffect\": ()=>{\n                    window.removeEventListener('scroll', handleScroll);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"ProfessionalHeader.useEffect\"];\n        }\n    }[\"ProfessionalHeader.useEffect\"], []);\n    // إغلاق القائمة عند الضغط على Escape\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProfessionalHeader.useEffect\": ()=>{\n            const handleEscape = {\n                \"ProfessionalHeader.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape') {\n                        setIsMenuOpen(false);\n                    }\n                }\n            }[\"ProfessionalHeader.useEffect.handleEscape\"];\n            if (isMenuOpen) {\n                document.addEventListener('keydown', handleEscape);\n                document.body.style.overflow = 'hidden';\n            } else {\n                document.body.style.overflow = 'unset';\n            }\n            return ({\n                \"ProfessionalHeader.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleEscape);\n                    document.body.style.overflow = 'unset';\n                }\n            })[\"ProfessionalHeader.useEffect\"];\n        }\n    }[\"ProfessionalHeader.useEffect\"], [\n        isMenuOpen\n    ]);\n    const navigationItems = [\n        {\n            href: '/',\n            label: 'الرئيسية',\n            icon: '🏠'\n        },\n        {\n            href: '/articles',\n            label: 'المقالات',\n            icon: '📰'\n        },\n        {\n            href: '/ai-tools',\n            label: 'أدوات الذكاء الاصطناعي',\n            icon: '🤖'\n        },\n        {\n            href: '/services',\n            label: 'الخدمات',\n            icon: '⚙️'\n        },\n        {\n            href: '/about',\n            label: 'من نحن',\n            icon: '👥'\n        },\n        {\n            href: '/contact',\n            label: 'اتصل بنا',\n            icon: '📞'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header header-interactive gradient-bg-interactive gpu-accelerated \".concat(isScrolled ? 'scrolled' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InteractiveEffects__WEBPACK_IMPORTED_MODULE_4__.InteractiveEffects, {\n                        target: \"header\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-container\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"logo-icon\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"ت\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"logo-text\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"logo-title\",\n                                                children: \"تكنو فلاش\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"logo-subtitle\",\n                                                children: \"أحدث أخبار التكنولوجيا\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 75,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"desktop-nav\",\n                                children: navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: item.href,\n                                        className: \"nav-link\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this),\n                                            item.label\n                                        ]\n                                    }, item.href, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"btn btn-ghost btn-sm user-menu\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"hidden lg:inline\",\n                                                children: \"بحث\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, this),\n                                    !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"user-menu\",\n                                        children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                    href: \"/admin\",\n                                                    className: \"btn btn-outline btn-sm\",\n                                                    children: \"لوحة التحكم\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: signOut,\n                                                    className: \"btn btn-ghost btn-sm text-error\",\n                                                    children: \"تسجيل الخروج\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 19\n                                        }, this) : null\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        className: \"mobile-menu-toggle \".concat(isMenuOpen ? 'open' : ''),\n                                        \"aria-label\": \"فتح القائمة\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mobile-menu-icon\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-menu-line\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mobile-menu-overlay lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        onClick: ()=>setIsMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mobile-menu-panel\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mobile-menu-content\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"mobile-nav\",\n                                children: [\n                                    navigationItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            onClick: ()=>setIsMenuOpen(false),\n                                            className: \"mobile-nav-link\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mobile-nav-icon\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.label\n                                            ]\n                                        }, item.href, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 19\n                                        }, this)),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mobile-user-menu\",\n                                        children: !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/admin\",\n                                                        onClick: ()=>setIsMenuOpen(false),\n                                                        className: \"mobile-nav-link\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mobile-nav-icon\",\n                                                                children: \"⚙️\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"لوحة التحكم\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            signOut();\n                                                            setIsMenuOpen(false);\n                                                        },\n                                                        className: \"mobile-nav-link text-error\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mobile-nav-icon\",\n                                                                children: \"\\uD83D\\uDEAA\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 29\n                                                            }, this),\n                                                            \"تسجيل الخروج\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 25\n                                            }, this) : null\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 lg:h-20\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ProfessionalHeader.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProfessionalHeader, \"aDNwtio0VWcrNxdRNzEfEOczo4U=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth\n    ];\n});\n_c = ProfessionalHeader;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalHeader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Byb2Zlc3Npb25hbEhlYWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUU2QjtBQUNlO0FBQ0s7QUFDdUM7QUFFakYsU0FBU0s7O0lBQ2QsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUdOLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQ08sWUFBWUMsY0FBYyxHQUFHUiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLEVBQUVTLElBQUksRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUUsR0FBR1QsOERBQU9BO0lBRTFDRCxnREFBU0E7d0NBQUM7WUFDUixNQUFNVzs2REFBZTtvQkFDbkJKLGNBQWNLLE9BQU9DLE9BQU8sR0FBRztnQkFDakM7O1lBRUEsTUFBTUM7NkRBQWU7b0JBQ25CLElBQUlGLE9BQU9HLFVBQVUsSUFBSSxNQUFNO3dCQUM3QlYsY0FBYztvQkFDaEI7Z0JBQ0Y7O1lBRUFPLE9BQU9JLGdCQUFnQixDQUFDLFVBQVVMO1lBQ2xDQyxPQUFPSSxnQkFBZ0IsQ0FBQyxVQUFVRjtZQUVsQztnREFBTztvQkFDTEYsT0FBT0ssbUJBQW1CLENBQUMsVUFBVU47b0JBQ3JDQyxPQUFPSyxtQkFBbUIsQ0FBQyxVQUFVSDtnQkFDdkM7O1FBQ0Y7dUNBQUcsRUFBRTtJQUVMLHFDQUFxQztJQUNyQ2QsZ0RBQVNBO3dDQUFDO1lBQ1IsTUFBTWtCOzZEQUFlLENBQUNDO29CQUNwQixJQUFJQSxFQUFFQyxHQUFHLEtBQUssVUFBVTt3QkFDdEJmLGNBQWM7b0JBQ2hCO2dCQUNGOztZQUVBLElBQUlELFlBQVk7Z0JBQ2RpQixTQUFTTCxnQkFBZ0IsQ0FBQyxXQUFXRTtnQkFDckNHLFNBQVNDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7WUFDakMsT0FBTztnQkFDTEgsU0FBU0MsSUFBSSxDQUFDQyxLQUFLLENBQUNDLFFBQVEsR0FBRztZQUNqQztZQUVBO2dEQUFPO29CQUNMSCxTQUFTSixtQkFBbUIsQ0FBQyxXQUFXQztvQkFDeENHLFNBQVNDLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7Z0JBQ2pDOztRQUNGO3VDQUFHO1FBQUNwQjtLQUFXO0lBRWYsTUFBTXFCLGtCQUFrQjtRQUN0QjtZQUFFQyxNQUFNO1lBQUtDLE9BQU87WUFBWUMsTUFBTTtRQUFLO1FBQzNDO1lBQUVGLE1BQU07WUFBYUMsT0FBTztZQUFZQyxNQUFNO1FBQUs7UUFDbkQ7WUFBRUYsTUFBTTtZQUFhQyxPQUFPO1lBQTBCQyxNQUFNO1FBQUs7UUFDakU7WUFBRUYsTUFBTTtZQUFhQyxPQUFPO1lBQVdDLE1BQU07UUFBSztRQUNsRDtZQUFFRixNQUFNO1lBQVVDLE9BQU87WUFBVUMsTUFBTTtRQUFLO1FBQzlDO1lBQUVGLE1BQU07WUFBWUMsT0FBTztZQUFZQyxNQUFNO1FBQUs7S0FDbkQ7SUFFRCxxQkFDRTs7MEJBQ0UsOERBQUNDO2dCQUFPQyxXQUFXLHFFQUFrRyxPQUE3QnhCLGFBQWEsYUFBYTs7a0NBQ2hILDhEQUFDSiw4RUFBa0JBO3dCQUFDNkIsUUFBTzs7Ozs7O2tDQUMzQiw4REFBQ0M7d0JBQUlGLFdBQVU7OzBDQUViLDhEQUFDaEMsa0RBQUlBO2dDQUFDNEIsTUFBSztnQ0FBSUksV0FBVTs7a0RBQ3ZCLDhEQUFDRTt3Q0FBSUYsV0FBVTtrREFDYiw0RUFBQ0c7c0RBQUs7Ozs7Ozs7Ozs7O2tEQUVSLDhEQUFDRDt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUNJO2dEQUFHSixXQUFVOzBEQUFhOzs7Ozs7MERBQzNCLDhEQUFDSztnREFBRUwsV0FBVTswREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FLakMsOERBQUNNO2dDQUFJTixXQUFVOzBDQUNaTCxnQkFBZ0JZLEdBQUcsQ0FBQyxDQUFDQyxxQkFDcEIsOERBQUN4QyxrREFBSUE7d0NBRUg0QixNQUFNWSxLQUFLWixJQUFJO3dDQUNmSSxXQUFVOzswREFFViw4REFBQ0c7Z0RBQUtILFdBQVU7MERBQVFRLEtBQUtWLElBQUk7Ozs7Ozs0Q0FDaENVLEtBQUtYLEtBQUs7O3VDQUxOVyxLQUFLWixJQUFJOzs7Ozs7Ozs7OzBDQVdwQiw4REFBQ007Z0NBQUlGLFdBQVU7O2tEQUViLDhEQUFDUzt3Q0FBT1QsV0FBVTs7MERBQ2hCLDhEQUFDVTtnREFBSVYsV0FBVTtnREFBVVcsTUFBSztnREFBT0MsUUFBTztnREFBZUMsU0FBUTswREFDakUsNEVBQUNDO29EQUFLQyxlQUFjO29EQUFRQyxnQkFBZTtvREFBUUMsYUFBYTtvREFBR0MsR0FBRTs7Ozs7Ozs7Ozs7MERBRXZFLDhEQUFDZjtnREFBS0gsV0FBVTswREFBbUI7Ozs7Ozs7Ozs7OztvQ0FJcEMsQ0FBQ3BCLHlCQUNBLDhEQUFDc0I7d0NBQUlGLFdBQVU7a0RBQ1p0QixxQkFDQyw4REFBQ3dCOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ2hDLGtEQUFJQTtvREFBQzRCLE1BQUs7b0RBQVNJLFdBQVU7OERBQXlCOzs7Ozs7OERBR3ZELDhEQUFDUztvREFDQ1UsU0FBU3hDO29EQUNUcUIsV0FBVTs4REFDWDs7Ozs7Ozs7Ozs7bURBSUQ7Ozs7OztrREFLUiw4REFBQ1M7d0NBQ0NVLFNBQVMsSUFBTTVDLGNBQWMsQ0FBQ0Q7d0NBQzlCMEIsV0FBVyxzQkFBK0MsT0FBekIxQixhQUFhLFNBQVM7d0NBQ3ZEOEMsY0FBVztrREFFWCw0RUFBQ2xCOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ0c7b0RBQUtILFdBQVU7Ozs7Ozs4REFDaEIsOERBQUNHO29EQUFLSCxXQUFVOzs7Ozs7OERBQ2hCLDhEQUFDRztvREFBS0gsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRekIxQiw0QkFDQyw4REFBQzRCO2dCQUFJRixXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQ0NGLFdBQVU7d0JBQ1ZtQixTQUFTLElBQU01QyxjQUFjOzs7Ozs7a0NBRS9CLDhEQUFDMkI7d0JBQUlGLFdBQVU7a0NBQ2IsNEVBQUNFOzRCQUFJRixXQUFVO3NDQUNiLDRFQUFDTTtnQ0FBSU4sV0FBVTs7b0NBQ1pMLGdCQUFnQlksR0FBRyxDQUFDLENBQUNDLHFCQUNwQiw4REFBQ3hDLGtEQUFJQTs0Q0FFSDRCLE1BQU1ZLEtBQUtaLElBQUk7NENBQ2Z1QixTQUFTLElBQU01QyxjQUFjOzRDQUM3QnlCLFdBQVU7OzhEQUVWLDhEQUFDRztvREFBS0gsV0FBVTs4REFBbUJRLEtBQUtWLElBQUk7Ozs7OztnREFDM0NVLEtBQUtYLEtBQUs7OzJDQU5OVyxLQUFLWixJQUFJOzs7OztrREFXbEIsOERBQUNNO3dDQUFJRixXQUFVO2tEQUNaLENBQUNwQix5QkFDQTtzREFDR0YscUJBQ0MsOERBQUN3QjtnREFBSUYsV0FBVTs7a0VBQ2IsOERBQUNoQyxrREFBSUE7d0RBQ0g0QixNQUFLO3dEQUNMdUIsU0FBUyxJQUFNNUMsY0FBYzt3REFDN0J5QixXQUFVOzswRUFFViw4REFBQ0c7Z0VBQUtILFdBQVU7MEVBQWtCOzs7Ozs7NERBQVM7Ozs7Ozs7a0VBRzdDLDhEQUFDUzt3REFDQ1UsU0FBUzs0REFDUHhDOzREQUNBSixjQUFjO3dEQUNoQjt3REFDQXlCLFdBQVU7OzBFQUVWLDhEQUFDRztnRUFBS0gsV0FBVTswRUFBa0I7Ozs7Ozs0REFBUzs7Ozs7Ozs7Ozs7O3VEQUk3Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV3BCLDhEQUFDRTtnQkFBSUYsV0FBVTs7Ozs7Ozs7QUFHckI7R0EvTGdCM0I7O1FBR3FCRiwwREFBT0E7OztLQUg1QkUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaXNtYWlsXFxEb3dubG9hZHNcXE5ldyBmb2xkZXIgKDQpXFx0ZWNobm8tZmxhc2hpXFxzcmNcXGNvbXBvbmVudHNcXFByb2Zlc3Npb25hbEhlYWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvQXV0aENvbnRleHQnO1xyXG5pbXBvcnQgeyBJbnRlcmFjdGl2ZUVmZmVjdHMsIEJ1dHRvbkhvdmVyRWZmZWN0IH0gZnJvbSAnQC9jb21wb25lbnRzL0ludGVyYWN0aXZlRWZmZWN0cyc7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvZmVzc2lvbmFsSGVhZGVyKCkge1xyXG4gIGNvbnN0IFtpc01lbnVPcGVuLCBzZXRJc01lbnVPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNTY3JvbGxlZCwgc2V0SXNTY3JvbGxlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgeyB1c2VyLCBzaWduT3V0LCBsb2FkaW5nIH0gPSB1c2VBdXRoKCk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjb25zdCBoYW5kbGVTY3JvbGwgPSAoKSA9PiB7XHJcbiAgICAgIHNldElzU2Nyb2xsZWQod2luZG93LnNjcm9sbFkgPiAyMCk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNvbnN0IGhhbmRsZVJlc2l6ZSA9ICgpID0+IHtcclxuICAgICAgaWYgKHdpbmRvdy5pbm5lcldpZHRoID49IDEwMjQpIHtcclxuICAgICAgICBzZXRJc01lbnVPcGVuKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignc2Nyb2xsJywgaGFuZGxlU2Nyb2xsKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBoYW5kbGVSZXNpemUpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdzY3JvbGwnLCBoYW5kbGVTY3JvbGwpO1xyXG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVzaXplJywgaGFuZGxlUmVzaXplKTtcclxuICAgIH07XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyDYpdi62YTYp9mCINin2YTZgtin2KbZhdipINi52YbYryDYp9mE2LbYuti3INi52YTZiSBFc2NhcGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaGFuZGxlRXNjYXBlID0gKGU6IEtleWJvYXJkRXZlbnQpID0+IHtcclxuICAgICAgaWYgKGUua2V5ID09PSAnRXNjYXBlJykge1xyXG4gICAgICAgIHNldElzTWVudU9wZW4oZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIGlmIChpc01lbnVPcGVuKSB7XHJcbiAgICAgIGRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBoYW5kbGVFc2NhcGUpO1xyXG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ2hpZGRlbic7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ3Vuc2V0JztcclxuICAgIH1cclxuXHJcbiAgICByZXR1cm4gKCkgPT4ge1xyXG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlRXNjYXBlKTtcclxuICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS5vdmVyZmxvdyA9ICd1bnNldCc7XHJcbiAgICB9O1xyXG4gIH0sIFtpc01lbnVPcGVuXSk7XHJcblxyXG4gIGNvbnN0IG5hdmlnYXRpb25JdGVtcyA9IFtcclxuICAgIHsgaHJlZjogJy8nLCBsYWJlbDogJ9in2YTYsdim2YrYs9mK2KknLCBpY29uOiAn8J+PoCcgfSxcclxuICAgIHsgaHJlZjogJy9hcnRpY2xlcycsIGxhYmVsOiAn2KfZhNmF2YLYp9mE2KfYqicsIGljb246ICfwn5OwJyB9LFxyXG4gICAgeyBocmVmOiAnL2FpLXRvb2xzJywgbGFiZWw6ICfYo9iv2YjYp9iqINin2YTYsNmD2KfYoSDYp9mE2KfYtdi32YbYp9i52YonLCBpY29uOiAn8J+klicgfSxcclxuICAgIHsgaHJlZjogJy9zZXJ2aWNlcycsIGxhYmVsOiAn2KfZhNiu2K/Zhdin2KonLCBpY29uOiAn4pqZ77iPJyB9LFxyXG4gICAgeyBocmVmOiAnL2Fib3V0JywgbGFiZWw6ICfZhdmGINmG2K3ZhicsIGljb246ICfwn5GlJyB9LFxyXG4gICAgeyBocmVmOiAnL2NvbnRhY3QnLCBsYWJlbDogJ9in2KrYtdmEINio2YbYpycsIGljb246ICfwn5OeJyB9LFxyXG4gIF07XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8PlxyXG4gICAgICA8aGVhZGVyIGNsYXNzTmFtZT17YGhlYWRlciBoZWFkZXItaW50ZXJhY3RpdmUgZ3JhZGllbnQtYmctaW50ZXJhY3RpdmUgZ3B1LWFjY2VsZXJhdGVkICR7aXNTY3JvbGxlZCA/ICdzY3JvbGxlZCcgOiAnJ31gfT5cclxuICAgICAgICA8SW50ZXJhY3RpdmVFZmZlY3RzIHRhcmdldD1cImhlYWRlclwiIC8+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZWFkZXItY29udGFpbmVyXCI+XHJcbiAgICAgICAgICB7LyogTG9nbyAqL31cclxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwibG9nb1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvZ28taWNvblwiPlxyXG4gICAgICAgICAgICAgIDxzcGFuPtiqPC9zcGFuPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsb2dvLXRleHRcIj5cclxuICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwibG9nby10aXRsZVwiPtiq2YPZhtmIINmB2YTYp9i0PC9oMT5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJsb2dvLXN1YnRpdGxlXCI+2KPYrdiv2Ksg2KPYrtio2KfYsSDYp9mE2KrZg9mG2YjZhNmI2KzZitinPC9wPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvTGluaz5cclxuXHJcbiAgICAgICAgICB7LyogRGVza3RvcCBOYXZpZ2F0aW9uICovfVxyXG4gICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJkZXNrdG9wLW5hdlwiPlxyXG4gICAgICAgICAgICB7bmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgIDxMaW5rXHJcbiAgICAgICAgICAgICAgICBrZXk9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm5hdi1saW5rXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yXCI+e2l0ZW0uaWNvbn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICB7aXRlbS5sYWJlbH1cclxuICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgPC9uYXY+XHJcblxyXG4gICAgICAgICAgey8qIFVzZXIgTWVudSAmIE1vYmlsZSBUb2dnbGUgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XHJcbiAgICAgICAgICAgIHsvKiBTZWFyY2ggQnV0dG9uICovfVxyXG4gICAgICAgICAgICA8YnV0dG9uIGNsYXNzTmFtZT1cImJ0biBidG4tZ2hvc3QgYnRuLXNtIHVzZXItbWVudVwiPlxyXG4gICAgICAgICAgICAgIDxzdmcgY2xhc3NOYW1lPVwidy00IGgtNFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxyXG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTIxIDIxbC02LTZtMi01YTcgNyAwIDExLTE0IDAgNyA3IDAgMDExNCAwelwiIC8+XHJcbiAgICAgICAgICAgICAgPC9zdmc+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaGlkZGVuIGxnOmlubGluZVwiPtio2K3Yqzwvc3Bhbj5cclxuICAgICAgICAgICAgPC9idXR0b24+XHJcblxyXG4gICAgICAgICAgICB7LyogVXNlciBNZW51ICovfVxyXG4gICAgICAgICAgICB7IWxvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidXNlci1tZW51XCI+XHJcbiAgICAgICAgICAgICAgICB7dXNlciA/IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWRtaW5cIiBjbGFzc05hbWU9XCJidG4gYnRuLW91dGxpbmUgYnRuLXNtXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3NpZ25PdXR9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidG4gYnRuLWdob3N0IGJ0bi1zbSB0ZXh0LWVycm9yXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICDYqtiz2KzZitmEINin2YTYrtix2YjYrFxyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICkgOiBudWxsfVxyXG4gICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgey8qIE1vYmlsZSBNZW51IFRvZ2dsZSAqL31cclxuICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oIWlzTWVudU9wZW4pfVxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YG1vYmlsZS1tZW51LXRvZ2dsZSAke2lzTWVudU9wZW4gPyAnb3BlbicgOiAnJ31gfVxyXG4gICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCLZgdiq2K0g2KfZhNmC2KfYptmF2KlcIlxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtb2JpbGUtbWVudS1pY29uXCI+XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtb2JpbGUtbWVudS1saW5lXCI+PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibW9iaWxlLW1lbnUtbGluZVwiPjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1vYmlsZS1tZW51LWxpbmVcIj48L3NwYW4+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvaGVhZGVyPlxyXG5cclxuICAgICAgey8qIE1vYmlsZSBNZW51ICovfVxyXG4gICAgICB7aXNNZW51T3BlbiAmJiAoXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtb2JpbGUtbWVudS1vdmVybGF5IGxnOmhpZGRlblwiPlxyXG4gICAgICAgICAgPGRpdiBcclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiXHJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9iaWxlLW1lbnUtcGFuZWxcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtb2JpbGUtbWVudS1jb250ZW50XCI+XHJcbiAgICAgICAgICAgICAgPG5hdiBjbGFzc05hbWU9XCJtb2JpbGUtbmF2XCI+XHJcbiAgICAgICAgICAgICAgICB7bmF2aWdhdGlvbkl0ZW1zLm1hcCgoaXRlbSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8TGlua1xyXG4gICAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5ocmVmfVxyXG4gICAgICAgICAgICAgICAgICAgIGhyZWY9e2l0ZW0uaHJlZn1cclxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc01lbnVPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtb2JpbGUtbmF2LWxpbmtcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibW9iaWxlLW5hdi1pY29uXCI+e2l0ZW0uaWNvbn08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAge2l0ZW0ubGFiZWx9XHJcbiAgICAgICAgICAgICAgICAgIDwvTGluaz5cclxuICAgICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICAgICAgXHJcbiAgICAgICAgICAgICAgICB7LyogTW9iaWxlIFVzZXIgTWVudSAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibW9iaWxlLXVzZXItbWVudVwiPlxyXG4gICAgICAgICAgICAgICAgICB7IWxvYWRpbmcgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7dXNlciA/IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0yXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPExpbmsgXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBocmVmPVwiL2FkbWluXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzTWVudU9wZW4oZmFsc2UpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibW9iaWxlLW5hdi1saW5rXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtb2JpbGUtbmF2LWljb25cIj7impnvuI88L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDZhNmI2K3YqSDYp9mE2KrYrdmD2YVcclxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L0xpbms+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2lnbk91dCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJc01lbnVPcGVuKGZhbHNlKTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtb2JpbGUtbmF2LWxpbmsgdGV4dC1lcnJvclwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibW9iaWxlLW5hdi1pY29uXCI+8J+aqjwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINiq2LPYrNmK2YQg2KfZhNiu2LHZiNisXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XHJcbiAgICAgICAgICAgICAgICAgICAgPC8+XHJcbiAgICAgICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L25hdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgKX1cclxuXHJcbiAgICAgIHsvKiBTcGFjZXIgZm9yIGZpeGVkIGhlYWRlciAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTE2IGxnOmgtMjBcIiAvPlxyXG4gICAgPC8+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiTGluayIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsIkludGVyYWN0aXZlRWZmZWN0cyIsIlByb2Zlc3Npb25hbEhlYWRlciIsImlzTWVudU9wZW4iLCJzZXRJc01lbnVPcGVuIiwiaXNTY3JvbGxlZCIsInNldElzU2Nyb2xsZWQiLCJ1c2VyIiwic2lnbk91dCIsImxvYWRpbmciLCJoYW5kbGVTY3JvbGwiLCJ3aW5kb3ciLCJzY3JvbGxZIiwiaGFuZGxlUmVzaXplIiwiaW5uZXJXaWR0aCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiaGFuZGxlRXNjYXBlIiwiZSIsImtleSIsImRvY3VtZW50IiwiYm9keSIsInN0eWxlIiwib3ZlcmZsb3ciLCJuYXZpZ2F0aW9uSXRlbXMiLCJocmVmIiwibGFiZWwiLCJpY29uIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwidGFyZ2V0IiwiZGl2Iiwic3BhbiIsImgxIiwicCIsIm5hdiIsIm1hcCIsIml0ZW0iLCJidXR0b24iLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJvbkNsaWNrIiwiYXJpYS1sYWJlbCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProfessionalHeader.tsx\n"));

/***/ })

});