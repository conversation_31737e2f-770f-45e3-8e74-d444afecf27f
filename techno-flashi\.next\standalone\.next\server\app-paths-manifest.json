{"/ads.txt/route": "app/ads.txt/route.js", "/api/ads/[id]/click/route": "app/api/ads/[id]/click/route.js", "/api/ads/[id]/route": "app/api/ads/[id]/route.js", "/api/ads/click/route": "app/api/ads/click/route.js", "/api/ads/route": "app/api/ads/route.js", "/api/ai-tools/route": "app/api/ai-tools/route.js", "/api/articles/route": "app/api/articles/route.js", "/api/newsletter/subscribe/route": "app/api/newsletter/subscribe/route.js", "/api/services/[id]/route": "app/api/services/[id]/route.js", "/api/services/route": "app/api/services/route.js", "/api/setup-storage/route": "app/api/setup-storage/route.js", "/api/simple-upload/route": "app/api/simple-upload/route.js", "/api/sitemap/route": "app/api/sitemap/route.js", "/api/test-ai-tools/route": "app/api/test-ai-tools/route.js", "/api/test-articles/route": "app/api/test-articles/route.js", "/api/test-ssg-build/route": "app/api/test-ssg-build/route.js", "/api/test-storage/route": "app/api/test-storage/route.js", "/api/upload-fallback/route": "app/api/upload-fallback/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/version/route": "app/api/version/route.js", "/rss.xml/route": "app/rss.xml/route.js", "/sitemap-articles.xml/route": "app/sitemap-articles.xml/route.js", "/sitemap-index.xml/route": "app/sitemap-index.xml/route.js", "/sitemap-tools.xml/route": "app/sitemap-tools.xml/route.js", "/manifest.webmanifest/route": "app/manifest.webmanifest/route.js", "/robots.txt/route": "app/robots.txt/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/pages/route": "app/api/pages/route.js", "/api/pages/[id]/route": "app/api/pages/[id]/route.js", "/_not-found/page": "app/_not-found/page.js", "/services/[id]/page": "app/services/[id]/page.js", "/seo-diagnosis/page": "app/seo-diagnosis/page.js", "/setup-admin/page": "app/setup-admin/page.js", "/test-animated-ads/page": "app/test-animated-ads/page.js", "/test-cleaner/page": "app/test-cleaner/page.js", "/test-icons/page": "app/test-icons/page.js", "/test/page": "app/test/page.js", "/about/page": "app/about/page.js", "/ai-tools/compare/page": "app/ai-tools/compare/page.js", "/ai-tools/page": "app/ai-tools/page.js", "/ai-tools/[slug]/page": "app/ai-tools/[slug]/page.js", "/contact/page": "app/contact/page.js", "/page/[slug]/page": "app/page/[slug]/page.js", "/articles/page": "app/articles/page.js", "/ai-tools/categories/page": "app/ai-tools/categories/page.js", "/page": "app/page.js", "/privacy-policy/page": "app/privacy-policy/page.js", "/articles/[slug]/page": "app/articles/[slug]/page.js", "/services/page": "app/services/page.js", "/terms-of-use/page": "app/terms-of-use/page.js", "/youtube/page": "app/youtube/page.js", "/admin/ads/[id]/edit/page": "app/admin/ads/[id]/edit/page.js", "/admin/ads/create/page": "app/admin/ads/create/page.js", "/admin/ads/ai-tools/page": "app/admin/ads/ai-tools/page.js", "/admin/ads/new/page": "app/admin/ads/new/page.js", "/admin/ads/migrate/page": "app/admin/ads/migrate/page.js", "/admin/ads/page": "app/admin/ads/page.js", "/admin/ads/sync/page": "app/admin/ads/sync/page.js", "/admin/ai-tools/new/page": "app/admin/ai-tools/new/page.js", "/admin/advanced-ads/page": "app/admin/advanced-ads/page.js", "/admin/ai-tools/edit/[id]/page": "app/admin/ai-tools/edit/[id]/page.js", "/admin/ai-tools/page": "app/admin/ai-tools/page.js", "/admin/articles/create/page": "app/admin/articles/create/page.js", "/admin/articles/new/page": "app/admin/articles/new/page.js", "/admin/articles/edit/[id]/page": "app/admin/articles/edit/[id]/page.js", "/admin/articles/page": "app/admin/articles/page.js", "/admin/media/page": "app/admin/media/page.js", "/admin/page": "app/admin/page.js", "/admin/pages/new/page": "app/admin/pages/new/page.js", "/admin/pages/edit/[id]/page": "app/admin/pages/edit/[id]/page.js", "/admin/services/[id]/edit/page": "app/admin/services/[id]/edit/page.js", "/admin/services/new/page": "app/admin/services/new/page.js", "/admin/setup-ads/page": "app/admin/setup-ads/page.js", "/admin/services/page": "app/admin/services/page.js", "/admin/pages/page": "app/admin/pages/page.js", "/admin/supabase-ads/page": "app/admin/supabase-ads/page.js", "/admin/test-ads-verification/page": "app/admin/test-ads-verification/page.js", "/admin/test-ads/page": "app/admin/test-ads/page.js", "/login/page": "app/login/page.js"}