(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4236],{48662:(e,s,i)=>{Promise.resolve().then(i.bind(i,87718))},87718:(e,s,i)=>{"use strict";i.r(s),i.d(s,{default:()=>a});var t=i(95155),l=i(3484);function a(){return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-center mb-8 text-gray-900",children:"\uD83C\uDFAC Animated Advertisements Test Page"}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 mb-8 shadow-sm",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-800",children:"\uD83D\uDCCD Header Position"}),(0,t.jsx)(l.HeaderAnimatedAd,{currentPage:"/test-animated-ads",className:"border-2 border-dashed border-blue-300 rounded-lg p-4",showDebug:!0})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 mb-8 shadow-sm",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-800",children:"\uD83D\uDCC4 Content Area"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"This is a test page to verify that all animated advertisements are working correctly. The ads should display with their respective animations: scrolling, fade, typewriter, bouncing, gradient, and sliding effects."}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"\uD83D\uDCCD In-Content Position"}),(0,t.jsx)(l.InContentAnimatedAd,{currentPage:"/test-animated-ads",className:"border-2 border-dashed border-green-300 rounded-lg p-4 my-6",showDebug:!0}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:'The animated ads should respect device targeting (desktop, mobile, tablet) and page targeting. All ads are configured with target_pages: ["*"] so they should appear on all pages.'}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"\uD83C\uDFAD Animation Types"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-gray-600 space-y-2 mb-6",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Horizontal Scrolling:"})," Text moves from right to left (Arabic) or left to right (English)"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Fade In/Out:"})," Text fades in and out with opacity transitions"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Typewriter Effect:"})," Text appears character by character"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Bouncing/Pulsing:"})," Text bounces and scales with transform animations"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Color-Changing Gradient:"})," Background and text colors shift continuously"]}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Sliding Text:"})," Text slides in from different directions"]})]}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-4 text-gray-800",children:"\uD83C\uDF10 Language Support"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Each animation type has both Arabic and English versions:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-gray-600 space-y-2 mb-6",children:[(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"Arabic:"}),' "إعلان هنا مقابل دولار واحد يومياً" (Cairo font)']}),(0,t.jsxs)("li",{children:[(0,t.jsx)("strong",{children:"English:"}),' "Advertisement Here for $1 Daily" (Inter font)']})]})]})}),(0,t.jsxs)("div",{className:"lg:col-span-1",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 mb-8 shadow-sm",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold mb-4 text-gray-800",children:"\uD83D\uDCCD Sidebar Position"}),(0,t.jsx)(l.SidebarAnimatedAd,{currentPage:"/test-animated-ads",className:"border-2 border-dashed border-purple-300 rounded-lg p-4",showDebug:!0})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-4 text-gray-800",children:"\uD83D\uDD27 Technical Details"}),(0,t.jsxs)("ul",{className:"text-sm text-gray-600 space-y-2",children:[(0,t.jsx)("li",{children:"✅ CSS animations only (no external JS)"}),(0,t.jsx)("li",{children:"✅ Responsive design"}),(0,t.jsx)("li",{children:"✅ Device targeting"}),(0,t.jsx)("li",{children:"✅ Page targeting"}),(0,t.jsx)("li",{children:"✅ Priority-based display"}),(0,t.jsx)("li",{children:"✅ Animation duration control"}),(0,t.jsx)("li",{children:"✅ Google Fonts integration"})]})]})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 mt-8 shadow-sm",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-gray-800",children:"\uD83D\uDCCD Footer Position"}),(0,t.jsx)(l.FooterAnimatedAd,{currentPage:"/test-animated-ads",className:"border-2 border-dashed border-red-300 rounded-lg p-4",showDebug:!0})]}),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mt-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-blue-900",children:"\uD83D\uDCCA Expected Results"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 text-blue-800",children:"\uD83C\uDFAF Positions"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Header: 2 ads (Arabic + English)"}),(0,t.jsx)("li",{children:"• Sidebar: 3 ads (Arabic + English)"}),(0,t.jsx)("li",{children:"• In-Content: 3 ads (Arabic + English)"}),(0,t.jsx)("li",{children:"• Footer: 4 ads (Arabic + English)"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2 text-blue-800",children:"\uD83C\uDFAC Animations"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Scrolling: 15s duration"}),(0,t.jsx)("li",{children:"• Fade: 4s duration"}),(0,t.jsx)("li",{children:"• Typewriter: 6s duration"}),(0,t.jsx)("li",{children:"• Bouncing: 2s duration"}),(0,t.jsx)("li",{children:"• Gradient: 8s duration"}),(0,t.jsx)("li",{children:"• Sliding: 5s duration"})]})]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[8888,1459,3586,8405,9420,6936,7979,1899,7098,4439,9744,2033,4495,5138,433,2652,3494,2574,2663,9173,3734,9473,871,8066,1842,680,7358],()=>s(48662)),_N_E=e.O()}]);