var _isArrayLike =
/*#__PURE__*/
require("./_isArrayLike.js");

var symIterator = typeof Symbol !== 'undefined' ? Symbol.iterator : '@@iterator';

function _createReduce(arrayReduce, methodReduce, iterableReduce) {
  return function _reduce(xf, acc, list) {
    if (_isArrayLike(list)) {
      return arrayReduce(xf, acc, list);
    }

    if (list == null) {
      return acc;
    }

    if (typeof list['fantasy-land/reduce'] === 'function') {
      return methodReduce(xf, acc, list, 'fantasy-land/reduce');
    }

    if (list[symIterator] != null) {
      return iterableReduce(xf, acc, list[symIterator]());
    }

    if (typeof list.next === 'function') {
      return iterableReduce(xf, acc, list);
    }

    if (typeof list.reduce === 'function') {
      return methodReduce(xf, acc, list, 'reduce');
    }

    throw new TypeError('reduce: list must be array or iterable');
  };
}

module.exports = _createReduce;