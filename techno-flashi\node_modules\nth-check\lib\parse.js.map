{"version": 3, "file": "parse.js", "sourceRoot": "https://raw.githubusercontent.com/fb55/nth-check/639fd2a4000b69f82350aad8c34cb43f77e483ba/src/", "sources": ["parse.ts"], "names": [], "mappings": ";AAAA,kEAAkE;;;AAElE,0EAA0E;AAC1E,IAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;AAChD,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/B,IAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAE/B;;;;;;GAMG;AACH,SAAgB,KAAK,CAAC,OAAe;IACjC,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;IAEvC,IAAI,OAAO,KAAK,MAAM,EAAE;QACpB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjB;SAAM,IAAI,OAAO,KAAK,KAAK,EAAE;QAC1B,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;KACjB;IAED,+DAA+D;IAE/D,IAAI,GAAG,GAAG,CAAC,CAAC;IAEZ,IAAI,CAAC,GAAG,CAAC,CAAC;IACV,IAAI,IAAI,GAAG,QAAQ,EAAE,CAAC;IACtB,IAAI,MAAM,GAAG,UAAU,EAAE,CAAC;IAE1B,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;QACrD,GAAG,EAAE,CAAC;QACN,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,CAAC,CAAC,CAAC;QAEzB,cAAc,EAAE,CAAC;QAEjB,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE;YACtB,IAAI,GAAG,QAAQ,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;YACjB,MAAM,GAAG,UAAU,EAAE,CAAC;SACzB;aAAM;YACH,IAAI,GAAG,MAAM,GAAG,CAAC,CAAC;SACrB;KACJ;IAED,kCAAkC;IAClC,IAAI,MAAM,KAAK,IAAI,IAAI,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE;QACzC,MAAM,IAAI,KAAK,CAAC,yCAAkC,OAAO,OAAI,CAAC,CAAC;KAClE;IAED,OAAO,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,CAAC;IAE1B,SAAS,QAAQ;QACb,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;YAC7B,GAAG,EAAE,CAAC;YACN,OAAO,CAAC,CAAC,CAAC;SACb;QAED,IAAI,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,GAAG,EAAE;YAC7B,GAAG,EAAE,CAAC;SACT;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED,SAAS,UAAU;QACf,IAAM,KAAK,GAAG,GAAG,CAAC;QAClB,IAAI,KAAK,GAAG,CAAC,CAAC;QAEd,OACI,GAAG,GAAG,OAAO,CAAC,MAAM;YACpB,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI;YAC/B,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,EACjC;YACE,KAAK,GAAG,KAAK,GAAG,EAAE,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YACtD,GAAG,EAAE,CAAC;SACT;QAED,4CAA4C;QAC5C,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IACxC,CAAC;IAED,SAAS,cAAc;QACnB,OACI,GAAG,GAAG,OAAO,CAAC,MAAM;YACpB,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,EACzC;YACE,GAAG,EAAE,CAAC;SACT;IACL,CAAC;AACL,CAAC;AA7ED,sBA6EC"}