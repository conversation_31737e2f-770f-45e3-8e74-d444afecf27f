(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={712:(e,r,s)=>{Promise.resolve().then(s.bind(s,12454))},1132:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11461:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=s(65239),o=s(48088),l=s(88170),n=s.n(l),a=s(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);s.d(r,i);let d={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\layout.tsx"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]},{layout:[()=>Promise.resolve().then(s.bind(s,3628)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[],apple:[],openGraph:[],twitter:[],manifest:"/manifest.webmanifest"}}]}.children,c=["C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\admin\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},11997:e=>{"use strict";e.exports=require("punycode")},12454:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>i});var t=s(60687),o=s(63213),l=s(43210),n=s(85814),a=s.n(n);let i=function(){let{user:e,signOut:r}=(0,o.A)(),[s,n]=(0,l.useState)(!1);return(0,t.jsx)("div",{className:"admin-container bg-dark-background min-h-screen",suppressHydrationWarning:!0,children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",style:{color:"#000000"},children:"أهلاً بك في لوحة التحكم"}),(0,t.jsx)("button",{onClick:r,className:"px-4 py-2 font-bold bg-red-600 rounded-md hover:bg-red-700",style:{color:"#ffffff"},children:"تسجيل الخروج"})]}),s&&e&&(0,t.jsxs)("p",{style:{color:"#000000"},children:["تم تسجيل دخولك بنجاح باستخدام البريد الإلكتروني: ",e.email]}),(0,t.jsx)("p",{className:"mt-4",style:{color:"#000000"},children:"من هنا يمكنك إدارة المقالات والأدوات والخدمات والإعلانات."}),(0,t.jsxs)("div",{className:"mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6",children:[(0,t.jsxs)(a(),{href:"/admin/articles",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCDD"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"المقالات"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"إدارة المقالات والمحتوى"})]}),(0,t.jsxs)(a(),{href:"/admin/ai-tools",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83E\uDD16"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"أدوات الذكاء الاصطناعي"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"إدارة أدوات الذكاء الاصطناعي"})]}),(0,t.jsxs)(a(),{href:"/admin/services",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDEE0️"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"الخدمات"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"إدارة الخدمات المقدمة"})]}),(0,t.jsxs)(a(),{href:"/admin/advanced-ads",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83C\uDFAF"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"نظام الإعلانات المتقدم"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"إدارة شاملة للإعلانات متعددة الشبكات"})]}),(0,t.jsxs)(a(),{href:"/admin/ads",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCE2"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"الإعلانات التقليدية"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"النظام القديم لإدارة الإعلانات"})]}),(0,t.jsxs)(a(),{href:"/admin/pages",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDCC4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"الصفحات"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"إدارة الصفحات الثابتة"})]}),(0,t.jsxs)(a(),{href:"/admin/media",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDDBC️"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"الوسائط"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"رفع الصور وإدارة الفيديوهات"})]}),(0,t.jsxs)(a(),{href:"/test-ads-comprehensive",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83E\uDDEA"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"اختبار الإعلانات"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"اختبار شامل لجميع أنواع الإعلانات"})]}),(0,t.jsxs)(a(),{href:"/test-advertisement-fixes",className:"bg-white p-6 rounded-lg border border-gray-300 hover:border-blue-500 transition-all duration-300 text-center group",style:{backgroundColor:"#ffffff"},children:[(0,t.jsx)("div",{className:"text-4xl mb-3 group-hover:scale-110 transition-transform duration-300",children:"\uD83D\uDD27"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",style:{color:"#000000"},children:"اختبار الإصلاحات"}),(0,t.jsx)("p",{className:"text-sm",style:{color:"#666666"},children:"اختبار إصلاحات نظام الإعلانات"})]})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,r,s)=>{"use strict";s.d(r,{ProtectedRoute:()=>n});var t=s(60687),o=s(63213),l=s(16189);function n({children:e}){let{user:r,loading:s}=(0,o.A)();return((0,l.useRouter)(),s)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",suppressHydrationWarning:!0,children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,t.jsx)("p",{style:{color:"#000000"},children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,t.jsx)(t.Fragment,{children:e}):null}s(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37160:(e,r,s)=>{Promise.resolve().then(s.bind(s,1132))},37331:(e,r,s)=>{Promise.resolve().then(s.bind(s,67083))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,r,s)=>{"use strict";s.d(r,{ProtectedRoute:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97579:(e,r,s)=>{Promise.resolve().then(s.bind(s,20769))},99111:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>l});var t=s(37413),o=s(67083);function l({children:e}){return(0,t.jsx)(o.ProtectedRoute,{children:(0,t.jsx)("div",{className:"admin-container",suppressHydrationWarning:!0,children:e})})}s(31240)}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4447,6437,2675,3595],()=>s(11461));module.exports=t})();