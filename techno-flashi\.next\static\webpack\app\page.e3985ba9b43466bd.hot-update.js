/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CUniversalAdDisplay.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CUniversalAdDisplay.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/AnimatedAdRenderer.tsx */ \"(app-pages-browser)/./src/components/ads/AnimatedAdRenderer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/TechnoFlashBanner.tsx */ \"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ads/UniversalAdDisplay.tsx */ \"(app-pages-browser)/./src/components/ads/UniversalAdDisplay.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/LatestAIToolsSection.tsx */ \"(app-pages-browser)/./src/components/LatestAIToolsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/NewsletterSubscription.tsx */ \"(app-pages-browser)/./src/components/NewsletterSubscription.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformanceOptimizer.tsx */ \"(app-pages-browser)/./src/components/PerformanceOptimizer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SocialShare.tsx */ \"(app-pages-browser)/./src/components/SocialShare.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SponsorsSection.tsx */ \"(app-pages-browser)/./src/components/SponsorsSection.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/styles/critical-homepage.css */ \"(app-pages-browser)/./src/styles/critical-homepage.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CAnimatedAdRenderer.tsx%22%2C%22ids%22%3A%5B%22InContentAnimatedAd%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CTechnoFlashBanner.tsx%22%2C%22ids%22%3A%5B%22TechnoFlashContentBanner%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5Cads%5C%5CUniversalAdDisplay.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CLatestAIToolsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CNewsletterSubscription.tsx%22%2C%22ids%22%3A%5B%22NewsletterSubscription%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CPerformanceOptimizer.tsx%22%2C%22ids%22%3A%5B%22PerformanceOptimizer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSocialShare.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Ccomponents%5C%5CSponsorsSection.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cismail%5C%5CDownloads%5C%5CNew%20folder%20(4)%5C%5Ctechno-flashi%5C%5Csrc%5C%5Cstyles%5C%5Ccritical-homepage.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ads/UniversalAdDisplay.tsx":
/*!***************************************************!*\
  !*** ./src/components/ads/UniversalAdDisplay.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UniversalAdDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction UniversalAdDisplay(param) {\n    let { position, className = '', fallbackAd } = param;\n    _s();\n    const [ads, setAds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UniversalAdDisplay.useEffect\": ()=>{\n            const fetchAds = {\n                \"UniversalAdDisplay.useEffect.fetchAds\": async ()=>{\n                    try {\n                        const response = await fetch(\"/api/ads?placement=\".concat(position, \"&isActive=true\"));\n                        if (!response.ok) {\n                            throw new Error('Failed to fetch ads');\n                        }\n                        const data = await response.json();\n                        setAds(data.ads || []);\n                    } catch (err) {\n                        console.error('Error fetching ads:', err);\n                        setError(err instanceof Error ? err.message : 'Unknown error');\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"UniversalAdDisplay.useEffect.fetchAds\"];\n            fetchAds();\n        }\n    }[\"UniversalAdDisplay.useEffect\"], [\n        position\n    ]);\n    const handleAdClick = (ad)=>{\n        // تسجيل النقرة\n        fetch('/api/ads/click', {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                adId: ad.id,\n                position: position,\n                timestamp: new Date().toISOString()\n            })\n        }).catch(console.error);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-200 dark:bg-gray-700 rounded-lg h-24 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500 dark:text-gray-400 text-sm\",\n                    children: \"جاري تحميل الإعلان...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || ads.length === 0) {\n        // عرض الإعلان الاحتياطي إذا كان متوفراً\n        if (fallbackAd) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"my-4 \".concat(className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: fallbackAd.click_url,\n                    target: \"_blank\",\n                    rel: \"noopener noreferrer\",\n                    className: \"block\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6 text-center hover:opacity-90 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-bold mb-2\",\n                                children: fallbackAd.title\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm opacity-90\",\n                                children: fallbackAd.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    }\n    // اختيار إعلان عشوائي من الإعلانات المتاحة (أو حسب الأولوية)\n    const selectedAd = ads.sort((a, b)=>b.priority - a.priority)[0];\n    if (!selectedAd) return null;\n    // إذا كان الإعلان يحتوي على HTML مخصص\n    if (selectedAd.html_content) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"my-4 \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ad-container\",\n                    dangerouslySetInnerHTML: {\n                        __html: selectedAd.html_content\n                    },\n                    onClick: ()=>handleAdClick(selectedAd)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                selectedAd.css_content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"style\", {\n                    dangerouslySetInnerHTML: {\n                        __html: selectedAd.css_content\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this),\n                selectedAd.javascript_content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: selectedAd.javascript_content\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    }\n    // إعلان صورة عادي\n    if (selectedAd.image_url) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"my-4 \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                href: selectedAd.click_url || '#',\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                onClick: ()=>handleAdClick(selectedAd),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-auto aspect-[8/1] bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden hover:opacity-90 transition-opacity\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        src: selectedAd.image_url,\n                        alt: selectedAd.title || selectedAd.name,\n                        fill: true,\n                        style: {\n                            objectFit: 'cover'\n                        },\n                        className: \"rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n            lineNumber: 138,\n            columnNumber: 7\n        }, this);\n    }\n    // إعلان نصي\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"my-4 \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n            href: selectedAd.click_url || '#',\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            onClick: ()=>handleAdClick(selectedAd),\n            className: \"block\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 rounded-lg p-4 text-center hover:opacity-90 transition-opacity\",\n                children: [\n                    selectedAd.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-bold mb-2 text-gray-900 dark:text-white\",\n                        children: selectedAd.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, this),\n                    selectedAd.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-700 dark:text-gray-300\",\n                        children: selectedAd.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n            lineNumber: 162,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\ads\\\\UniversalAdDisplay.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n_s(UniversalAdDisplay, \"VFlxUKEDrDJnKtFlCvwG1YNgxVM=\");\n_c = UniversalAdDisplay;\nvar _c;\n$RefreshReg$(_c, \"UniversalAdDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ads/UniversalAdDisplay.tsx\n"));

/***/ })

});