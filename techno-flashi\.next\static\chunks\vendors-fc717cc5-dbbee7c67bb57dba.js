"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7098],{3507:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return n}});let a=l(68946);function r(e){return void 0!==e}function n(e,t){var l,n;let u=null==(l=t.shouldScroll)||l,o=e.nextUrl;if(r(t.patchedTree)){let l=(0,a.computeChangedPath)(e.tree,t.patchedTree);l?o=l:o||(o=e.canonicalUrl)}return{canonicalUrl:r(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:r(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:r(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:r(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!u&&(!!r(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:u?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:u?null!=(n=null==t?void 0:t.scrollableSegments)?n:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:r(t.patchedTree)?t.patchedTree:e.tree,nextUrl:o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4466:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,l,n){let u=n.length<=2,[o,f]=n,d=(0,a.createRouterCacheKey)(f),i=l.parallelRoutes.get(o);if(!i)return;let c=t.parallelRoutes.get(o);if(c&&c!==i||(c=new Map(i),t.parallelRoutes.set(o,c)),u)return void c.delete(d);let s=i.get(d),p=c.get(d);p&&s&&(p===s&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes)},c.set(d,p)),e(p,s,(0,r.getNextFlightSegmentPath)(n)))}}});let a=l(85637),r=l(22561);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},11139:(e,t)=>{function l(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return l}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19880:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,l,n){let u=n.length<=2,[o,f]=n,d=(0,r.createRouterCacheKey)(f),i=l.parallelRoutes.get(o),c=t.parallelRoutes.get(o);c&&c!==i||(c=new Map(i),t.parallelRoutes.set(o,c));let s=null==i?void 0:i.get(d),p=c.get(d);if(u){p&&p.lazyData&&p!==s||c.set(d,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!p||!s){p||c.set(d,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return p===s&&(p={lazyData:p.lazyData,rsc:p.rsc,prefetchRsc:p.prefetchRsc,head:p.head,prefetchHead:p.prefetchHead,parallelRoutes:new Map(p.parallelRoutes),loading:p.loading},c.set(d,p)),e(p,s,(0,a.getNextFlightSegmentPath)(n))}}});let a=l(22561),r=l(85637);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},31295:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let a=l(6966),r=l(95155),n=a._(l(12115)),u=l(95227);function o(){let e=(0,n.useContext)(u.TemplateContext);return(0,r.jsx)(r.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34758:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,l,n,u,o,f,d){if(0===Object.keys(u[1]).length){l.head=f;return}for(let i in u[1]){let c,s=u[1][i],p=s[0],h=(0,a.createRouterCacheKey)(p),g=null!==o&&void 0!==o[2][i]?o[2][i]:null;if(n){let a=n.parallelRoutes.get(i);if(a){let n,u=(null==d?void 0:d.kind)==="auto"&&d.status===r.PrefetchCacheEntryStatus.reusable,o=new Map(a),c=o.get(h);n=null!==g?{lazyData:null,rsc:g[1],prefetchRsc:null,head:null,prefetchHead:null,loading:g[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),navigatedAt:t}:u&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),loading:null,navigatedAt:t},o.set(h,n),e(t,n,c,s,g||null,f,d),l.parallelRoutes.set(i,o);continue}}if(null!==g){let e=g[1],l=g[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:l,navigatedAt:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let y=l.parallelRoutes.get(i);y?y.set(h,c):l.parallelRoutes.set(i,new Map([[h,c]])),e(t,c,void 0,s,g,f,d)}}}});let a=l(85637),r=l(69818);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39234:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,l){let a=t[0],r=l[0];if(Array.isArray(a)&&Array.isArray(r)){if(a[0]!==r[0]||a[2]!==r[2])return!0}else if(a!==r)return!0;if(t[4])return!l[4];if(l[4])return!0;let n=Object.values(t[1])[0],u=Object.values(l[1])[0];return!n||!u||e(n,u)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42004:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return r}});let a=l(85637);function r(e,t,l){for(let r in l[1]){let n=l[1][r][0],u=(0,a.createRouterCacheKey)(n),o=t.parallelRoutes.get(r);if(o){let t=new Map(o);t.delete(u),e.parallelRoutes.set(r,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57442:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,l,a,f){let d,[i,c,s,p,h]=l;if(1===t.length){let e=o(l,a);return(0,u.addRefreshMarkerToActiveParallelSegments)(e,f),e}let[g,y]=t;if(!(0,n.matchSegment)(g,i))return null;if(2===t.length)d=o(c[y],a);else if(null===(d=e((0,r.getNextFlightSegmentPath)(t),c[y],a,f)))return null;let _=[t[0],{...c,[y]:d},s,p];return h&&(_[4]=!0),(0,u.addRefreshMarkerToActiveParallelSegments)(_,f),_}}});let a=l(8291),r=l(22561),n=l(31127),u=l(44908);function o(e,t){let[l,r]=e,[u,f]=t;if(u===a.DEFAULT_SEGMENT_KEY&&l!==a.DEFAULT_SEGMENT_KEY)return e;if((0,n.matchSegment)(l,u)){let t={};for(let e in r)void 0!==f[e]?t[e]=o(r[e],f[e]):t[e]=r[e];for(let e in f)t[e]||(t[e]=f[e]);let a=[l,t];return e[2]&&(a[2]=e[2]),e[3]&&(a[3]=e[3]),e[4]&&(a[4]=e[4]),a}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68946:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{computeChangedPath:function(){return i},extractPathFromFlightRouterState:function(){return d},getSelectedParams:function(){return function e(t,l){for(let a of(void 0===l&&(l={}),Object.values(t[1]))){let t=a[0],n=Array.isArray(t),u=n?t[1]:t;!u||u.startsWith(r.PAGE_SEGMENT_KEY)||(n&&("c"===t[2]||"oc"===t[2])?l[t[0]]=t[1].split("/"):n&&(l[t[0]]=t[1]),l=e(a,l))}return l}}});let a=l(47755),r=l(8291),n=l(31127),u=e=>"/"===e[0]?e.slice(1):e,o=e=>"string"==typeof e?"children"===e?"":e:e[1];function f(e){return e.reduce((e,t)=>""===(t=u(t))||(0,r.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function d(e){var t;let l=Array.isArray(e[0])?e[0][1]:e[0];if(l===r.DEFAULT_SEGMENT_KEY||a.INTERCEPTION_ROUTE_MARKERS.some(e=>l.startsWith(e)))return;if(l.startsWith(r.PAGE_SEGMENT_KEY))return"";let n=[o(l)],u=null!=(t=e[1])?t:{},i=u.children?d(u.children):void 0;if(void 0!==i)n.push(i);else for(let[e,t]of Object.entries(u)){if("children"===e)continue;let l=d(t);void 0!==l&&n.push(l)}return f(n)}function i(e,t){let l=function e(t,l){let[r,u]=t,[f,i]=l,c=o(r),s=o(f);if(a.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)||s.startsWith(e)))return"";if(!(0,n.matchSegment)(r,f)){var p;return null!=(p=d(l))?p:""}for(let t in u)if(i[t]){let l=e(u[t],i[t]);if(null!==l)return o(f)+"/"+l}return null}(e,t);return null==l||"/"===l?l:f(l.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70878:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return n}});let a=l(34758),r=l(73118);function n(e,t,l,n,u){let{tree:o,seedData:f,head:d,isRootRender:i}=n;if(null===f)return!1;if(i){let r=f[1];l.loading=f[3],l.rsc=r,l.prefetchRsc=null,(0,a.fillLazyItemsTillLeafWithHead)(e,l,t,o,f,d,u)}else l.rsc=t.rsc,l.prefetchRsc=t.prefetchRsc,l.parallelRoutes=new Map(t.parallelRoutes),l.loading=t.loading,(0,r.fillCacheWithNewSubTreeData)(e,l,t,n,u);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73118:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{fillCacheWithNewSubTreeData:function(){return f},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return d}});let a=l(42004),r=l(34758),n=l(85637),u=l(8291);function o(e,t,l,o,f,d){let{segmentPath:i,seedData:c,tree:s,head:p}=o,h=t,g=l;for(let t=0;t<i.length;t+=2){let l=i[t],o=i[t+1],y=t===i.length-2,_=(0,n.createRouterCacheKey)(o),R=g.parallelRoutes.get(l);if(!R)continue;let b=h.parallelRoutes.get(l);b&&b!==R||(b=new Map(R),h.parallelRoutes.set(l,b));let v=R.get(_),P=b.get(_);if(y){if(c&&(!P||!P.lazyData||P===v)){let t=c[0],l=c[1],n=c[3];P={lazyData:null,rsc:d||t!==u.PAGE_SEGMENT_KEY?l:null,prefetchRsc:null,head:null,prefetchHead:null,loading:n,parallelRoutes:d&&v?new Map(v.parallelRoutes):new Map,navigatedAt:e},v&&d&&(0,a.invalidateCacheByRouterState)(P,v,s),d&&(0,r.fillLazyItemsTillLeafWithHead)(e,P,v,s,c,p,f),b.set(_,P)}continue}P&&v&&(P===v&&(P={lazyData:P.lazyData,rsc:P.rsc,prefetchRsc:P.prefetchRsc,head:P.head,prefetchHead:P.prefetchHead,parallelRoutes:new Map(P.parallelRoutes),loading:P.loading},b.set(_,P)),h=P,g=v)}}function f(e,t,l,a,r){o(e,t,l,a,r,!0)}function d(e,t,l,a,r){o(e,t,l,a,r,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85637:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return r}});let a=l(8291);function r(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(a.PAGE_SEGMENT_KEY)?a.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88586:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{createFetch:function(){return g},createFromNextReadableStream:function(){return y},fetchServerResponse:function(){return h},urlToUrlWithoutFlightMarker:function(){return c}});let a=l(3269),r=l(53806),n=l(31818),u=l(69818),o=l(22561),f=l(85624),d=l(58969),{createFromReadableStream:i}=l(34979);function c(e){let t=new URL(e,location.origin);return t.searchParams.delete(a.NEXT_RSC_UNION_QUERY),t}function s(e){return{flightData:c(e).toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}let p=new AbortController;async function h(e,t){let{flightRouterState:l,nextUrl:r,prefetchKind:n}=t,d={[a.RSC_HEADER]:"1",[a.NEXT_ROUTER_STATE_TREE_HEADER]:(0,o.prepareFlightRouterStateForRequest)(l,t.isHmrRefresh)};n===u.PrefetchKind.AUTO&&(d[a.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(d[a.NEXT_URL]=r);try{var i;let t=n?n===u.PrefetchKind.TEMPORARY?"high":"low":"auto",l=await g(e,d,t,p.signal),r=c(l.url),h=l.redirected?r:void 0,_=l.headers.get("content-type")||"",R=!!(null==(i=l.headers.get("vary"))?void 0:i.includes(a.NEXT_URL)),b=!!l.headers.get(a.NEXT_DID_POSTPONE_HEADER),v=l.headers.get(a.NEXT_ROUTER_STALE_TIME_HEADER),P=null!==v?1e3*parseInt(v,10):-1;if(!_.startsWith(a.RSC_CONTENT_TYPE_HEADER)||!l.ok||!l.body)return e.hash&&(r.hash=e.hash),s(r.toString());let M=b?function(e){let t=e.getReader();return new ReadableStream({async pull(e){for(;;){let{done:l,value:a}=await t.read();if(!l){e.enqueue(a);continue}return}}})}(l.body):l.body,m=await y(M);if((0,f.getAppBuildId)()!==m.b)return s(l.url);return{flightData:(0,o.normalizeFlightData)(m.f),canonicalUrl:h,couldBeIntercepted:R,prerendered:m.S,postponed:b,staleTime:P}}catch(t){return p.signal.aborted||console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),{flightData:e.toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}}function g(e,t,l,a){let r=new URL(e);return(0,d.setCacheBustingSearchParam)(r,t),fetch(r,{credentials:"same-origin",headers:t,priority:l||void 0,signal:a})}function y(e){return i(e,{callServer:r.callServer,findSourceMapURL:n.findSourceMapURL})}window.addEventListener("pagehide",()=>{p.abort()}),window.addEventListener("pageshow",()=>{p=new AbortController}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},93567:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return i}});let a=l(11139),r=l(34758),n=l(68946),u=l(31518),o=l(69818),f=l(44908),d=l(22561);function i(e){var t,l;let{navigatedAt:i,initialFlightData:c,initialCanonicalUrlParts:s,initialParallelRoutes:p,location:h,couldBeIntercepted:g,postponed:y,prerendered:_}=e,R=s.join("/"),b=(0,d.getFlightDataPartsFromPath)(c[0]),{tree:v,seedData:P,head:M}=b,m={lazyData:null,rsc:null==P?void 0:P[1],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:p,loading:null!=(t=null==P?void 0:P[3])?t:null,navigatedAt:i},j=h?(0,a.createHrefFromUrl)(h):R;(0,f.addRefreshMarkerToActiveParallelSegments)(v,j);let E=new Map;(null===p||0===p.size)&&(0,r.fillLazyItemsTillLeafWithHead)(i,m,void 0,v,P,M,void 0);let O={tree:v,cache:m,prefetchCache:E,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:j,nextUrl:null!=(l=(0,n.extractPathFromFlightRouterState)(v)||(null==h?void 0:h.pathname))?l:null};if(h){let e=new URL(""+h.pathname+h.search,h.origin);(0,u.createSeededPrefetchCacheEntry)({url:e,data:{flightData:[b],canonicalUrl:void 0,couldBeIntercepted:!!g,prerendered:_,postponed:y,staleTime:_&&1?u.STATIC_STALETIME_MS:-1},tree:O.tree,prefetchCache:O.prefetchCache,nextUrl:O.nextUrl,kind:_?o.PrefetchKind.FULL:o.PrefetchKind.AUTO})}return O}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95563:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var l in t)Object.defineProperty(e,l,{enumerable:!0,get:t[l]})}(t,{addSearchParamsToPageSegments:function(){return c},handleAliasedPrefetchEntry:function(){return i}});let a=l(8291),r=l(56158),n=l(57442),u=l(11139),o=l(85637),f=l(73118),d=l(3507);function i(e,t,l,i,s){let p,h=t.tree,g=t.cache,y=(0,u.createHrefFromUrl)(i);if("string"==typeof l)return!1;for(let t of l){if(!function e(t){if(!t)return!1;let l=t[2];if(t[3])return!0;for(let t in l)if(e(l[t]))return!0;return!1}(t.seedData))continue;let l=t.tree;l=c(l,Object.fromEntries(i.searchParams));let{seedData:u,isRootRender:d,pathToSegment:s}=t,_=["",...s];l=c(l,Object.fromEntries(i.searchParams));let R=(0,n.applyRouterStatePatchToTree)(_,h,l,y),b=(0,r.createEmptyCacheNode)();if(d&&u){let t=u[1];b.loading=u[3],b.rsc=t,function e(t,l,r,n,u){if(0!==Object.keys(n[1]).length)for(let f in n[1]){let d,i=n[1][f],c=i[0],s=(0,o.createRouterCacheKey)(c),p=null!==u&&void 0!==u[2][f]?u[2][f]:null;if(null!==p){let e=p[1],l=p[3];d={lazyData:null,rsc:c.includes(a.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:l,navigatedAt:t}}else d={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let h=l.parallelRoutes.get(f);h?h.set(s,d):l.parallelRoutes.set(f,new Map([[s,d]])),e(t,d,r,i,p)}}(e,b,g,l,u)}else b.rsc=g.rsc,b.prefetchRsc=g.prefetchRsc,b.loading=g.loading,b.parallelRoutes=new Map(g.parallelRoutes),(0,f.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,g,t);R&&(h=R,g=b,p=!0)}return!!p&&(s.patchedTree=h,s.cache=g,s.canonicalUrl=y,s.hashFragment=i.hash,(0,d.handleMutable)(t,s))}function c(e,t){let[l,r,...n]=e;if(l.includes(a.PAGE_SEGMENT_KEY))return[(0,a.addSearchParamsIfPageSegment)(l,t),r,...n];let u={};for(let[e,l]of Object.entries(r))u[e]=c(l,t);return[l,u,...n]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96375:(e,t,l)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return r}});let a=l(43894);function r(e,t,l){return(0,a.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}}]);