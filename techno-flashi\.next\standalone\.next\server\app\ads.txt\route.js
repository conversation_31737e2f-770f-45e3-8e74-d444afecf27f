(()=>{var e={};e.id=1659,e.ids=[1659],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76918:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var a={};r.r(a),r.d(a,{GET:()=>i});var s=r(96559),o=r(48088),n=r(37719),c=r(32190);async function i(e){try{let t=(e.headers.get("host")||"tflash.site").replace("www.",""),r=`https://srv.adstxtmanager.com/19390/${t}`,a=await fetch(r,{headers:{"User-Agent":"TFlash-Bot/1.0"},next:{revalidate:3600}});if(!a.ok){let e=process.env.NEXT_PUBLIC_ADSENSE_PUBLISHER_ID||"ca-pub-1234567890123456",r=`# ads.txt for ${t}
# Managed by Ezoic
ezoic.com, 19390, DIRECT
google.com, ${e}, RESELLER, f08c47fec0942fa0
google.com, ${e}, DIRECT, f08c47fec0942fa0

# Contact: <EMAIL>
# Last updated: ${new Date().toISOString().split("T")[0]}`;return new c.NextResponse(r,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=3600"}})}let s=await a.text();return new c.NextResponse(s,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=3600","X-Ads-Txt-Source":"ezoic-managed"}})}catch(r){console.error("Error fetching ads.txt:",r);let e=process.env.NEXT_PUBLIC_ADSENSE_PUBLISHER_ID||"ca-pub-1234567890123456",t=`# ads.txt for tflash.site
# Emergency fallback content
ezoic.com, 19390, DIRECT
google.com, ${e}, RESELLER, f08c47fec0942fa0

# Contact: <EMAIL>`;return new c.NextResponse(t,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=300"}})}}let p=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/ads.txt/route",pathname:"/ads.txt",filename:"route",bundlePath:"app/ads.txt/route"},resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\New folder (4)\\techno-flashi\\src\\app\\ads.txt\\route.ts",nextConfigOutput:"standalone",userland:a}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:l}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4447,580],()=>r(76918));module.exports=a})();