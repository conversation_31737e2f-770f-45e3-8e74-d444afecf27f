"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/SimpleHeroSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/SimpleHeroSection.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleHeroSection: () => (/* binding */ SimpleHeroSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ SimpleHeroSection auto */ \nvar _s = $RefreshSig$();\n\n\nfunction SimpleHeroSection() {\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const mouseRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        x: 0,\n        y: 0\n    });\n    const particlesRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleHeroSection.useEffect\": ()=>{\n            const canvas = canvasRef.current;\n            if (!canvas) return;\n            const ctx = canvas.getContext('2d');\n            if (!ctx) return;\n            // إعداد الكانفاس\n            const resizeCanvas = {\n                \"SimpleHeroSection.useEffect.resizeCanvas\": ()=>{\n                    canvas.width = window.innerWidth;\n                    canvas.height = window.innerHeight;\n                }\n            }[\"SimpleHeroSection.useEffect.resizeCanvas\"];\n            resizeCanvas();\n            window.addEventListener('resize', resizeCanvas);\n            // إنشاء الجسيمات الذكية المتطورة مع تحسينات جديدة\n            const createParticles = {\n                \"SimpleHeroSection.useEffect.createParticles\": ()=>{\n                    const particles = [];\n                    const colors = [\n                        '#8b5cf6',\n                        '#a855f7',\n                        '#c084fc',\n                        '#d8b4fe',\n                        '#e879f9',\n                        '#f0abfc',\n                        '#fbbf24',\n                        '#f59e0b',\n                        '#06b6d4',\n                        '#0891b2',\n                        '#10b981',\n                        '#059669',\n                        '#f97316',\n                        '#ea580c',\n                        '#dc2626',\n                        '#b91c1c',\n                        '#7c3aed',\n                        '#6d28d9'\n                    ];\n                    for(let i = 0; i < 200; i++){\n                        particles.push({\n                            x: Math.random() * canvas.width,\n                            y: Math.random() * canvas.height,\n                            vx: (Math.random() - 0.5) * 2,\n                            vy: (Math.random() - 0.5) * 2,\n                            size: Math.random() * 8 + 2,\n                            color: colors[Math.floor(Math.random() * colors.length)],\n                            opacity: Math.random() * 0.8 + 0.2,\n                            pulse: Math.random() * Math.PI * 2,\n                            pulseSpeed: 0.008 + Math.random() * 0.04,\n                            rotationSpeed: (Math.random() - 0.5) * 0.05,\n                            magnetism: Math.random() * 0.8 + 0.6,\n                            trail: [],\n                            energy: Math.random() * 100 + 50,\n                            type: Math.floor(Math.random() * 3),\n                            phase: Math.random() * Math.PI * 2,\n                            amplitude: Math.random() * 20 + 10 // سعة الحركة الموجية\n                        });\n                    }\n                    particlesRef.current = particles;\n                }\n            }[\"SimpleHeroSection.useEffect.createParticles\"];\n            createParticles();\n            // معالج حركة الماوس\n            const handleMouseMove = {\n                \"SimpleHeroSection.useEffect.handleMouseMove\": (e)=>{\n                    mouseRef.current = {\n                        x: e.clientX / window.innerWidth * 2 - 1,\n                        y: -(e.clientY / window.innerHeight) * 2 + 1\n                    };\n                }\n            }[\"SimpleHeroSection.useEffect.handleMouseMove\"];\n            window.addEventListener('mousemove', handleMouseMove);\n            // دالة رسم السداسي\n            const drawHexagon = {\n                \"SimpleHeroSection.useEffect.drawHexagon\": (ctx, x, y, size)=>{\n                    ctx.beginPath();\n                    for(let i = 0; i < 6; i++){\n                        const angle = i * Math.PI / 3;\n                        const px = x + size * Math.cos(angle);\n                        const py = y + size * Math.sin(angle);\n                        if (i === 0) {\n                            ctx.moveTo(px, py);\n                        } else {\n                            ctx.lineTo(px, py);\n                        }\n                    }\n                    ctx.closePath();\n                    ctx.stroke();\n                }\n            }[\"SimpleHeroSection.useEffect.drawHexagon\"];\n            // حلقة الرسم المتطورة مع تأثيرات مبهرة\n            const animate = {\n                \"SimpleHeroSection.useEffect.animate\": ()=>{\n                    // مسح تدريجي بدلاً من المسح الكامل\n                    ctx.fillStyle = 'rgba(255, 255, 255, 0.05)';\n                    ctx.fillRect(0, 0, canvas.width, canvas.height);\n                    const time = Date.now() * 0.001;\n                    const mouseX = mouseRef.current.x * canvas.width * 0.5 + canvas.width * 0.5;\n                    const mouseY = mouseRef.current.y * canvas.height * 0.5 + canvas.height * 0.5;\n                    // رسم هالة تتبع الماوس المتوهجة المحسنة\n                    if (mouseRef.current.x !== 0 || mouseRef.current.y !== 0) {\n                        // هالة خارجية كبيرة\n                        const outerGlowSize = 150 + Math.sin(time * 2) * 30;\n                        const outerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, outerGlowSize);\n                        outerGradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 1.5) * 0.08, \")\"));\n                        outerGradient.addColorStop(0.2, \"rgba(236, 72, 153, \".concat(0.15 + Math.sin(time * 2) * 0.06, \")\"));\n                        outerGradient.addColorStop(0.4, \"rgba(251, 191, 36, \".concat(0.1 + Math.sin(time * 2.5) * 0.04, \")\"));\n                        outerGradient.addColorStop(0.6, \"rgba(6, 182, 212, \".concat(0.08 + Math.sin(time * 3) * 0.03, \")\"));\n                        outerGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = outerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, outerGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // هالة متوسطة\n                        const midGlowSize = 80 + Math.sin(time * 3) * 15;\n                        const midGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, midGlowSize);\n                        midGradient.addColorStop(0, \"rgba(139, 92, 246, \".concat(0.3 + Math.sin(time * 2.5) * 0.1, \")\"));\n                        midGradient.addColorStop(0.5, \"rgba(168, 85, 247, \".concat(0.2 + Math.sin(time * 3) * 0.08, \")\"));\n                        midGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = midGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, midGlowSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // دائرة مركزية نابضة\n                        const centerSize = 20 + Math.sin(time * 4) * 8;\n                        const centerGradient = ctx.createRadialGradient(mouseX, mouseY, 0, mouseX, mouseY, centerSize);\n                        centerGradient.addColorStop(0, \"rgba(255, 255, 255, \".concat(0.9 + Math.sin(time * 5) * 0.1, \")\"));\n                        centerGradient.addColorStop(0.7, \"rgba(168, 85, 247, \".concat(0.6 + Math.sin(time * 4) * 0.2, \")\"));\n                        centerGradient.addColorStop(1, 'rgba(168, 85, 247, 0)');\n                        ctx.fillStyle = centerGradient;\n                        ctx.beginPath();\n                        ctx.arc(mouseX, mouseY, centerSize, 0, Math.PI * 2);\n                        ctx.fill();\n                        // رسم موجات متطورة تنتشر من موضع الماوس\n                        for(let i = 0; i < 5; i++){\n                            const waveRadius = 30 + (time * 80 + i * 40) % 250;\n                            const waveAlpha = Math.max(0, 0.4 - waveRadius / 250 * 0.4);\n                            if (waveAlpha > 0.01) {\n                                // موجة رئيسية\n                                ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(waveAlpha, \")\");\n                                ctx.lineWidth = 3 - waveRadius / 250 * 2;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, waveRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة ثانوية متداخلة\n                                const secondaryRadius = waveRadius * 0.7;\n                                const secondaryAlpha = waveAlpha * 0.6;\n                                ctx.strokeStyle = \"rgba(236, 72, 153, \".concat(secondaryAlpha, \")\");\n                                ctx.lineWidth = 2 - secondaryRadius / 250 * 1.5;\n                                ctx.beginPath();\n                                ctx.arc(mouseX, mouseY, secondaryRadius, 0, Math.PI * 2);\n                                ctx.stroke();\n                                // موجة داخلية\n                                if (i % 2 === 0) {\n                                    const innerRadius = waveRadius * 0.4;\n                                    const innerAlpha = waveAlpha * 0.8;\n                                    ctx.strokeStyle = \"rgba(251, 191, 36, \".concat(innerAlpha, \")\");\n                                    ctx.lineWidth = 1.5;\n                                    ctx.beginPath();\n                                    ctx.arc(mouseX, mouseY, innerRadius, 0, Math.PI * 2);\n                                    ctx.stroke();\n                                }\n                            }\n                        }\n                    }\n                    // رسم موجات ديناميكية في الخلفية\n                    const waveCount = 5;\n                    for(let i = 0; i < waveCount; i++){\n                        ctx.beginPath();\n                        ctx.strokeStyle = \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i) * 0.01), \")\");\n                        ctx.lineWidth = 2;\n                        for(let x = 0; x <= canvas.width; x += 10){\n                            const y = canvas.height * 0.5 + Math.sin(x * 0.01 + time + i) * 50 * (i + 1) + Math.sin(x * 0.005 + time * 0.5 + i) * 30;\n                            if (x === 0) {\n                                ctx.moveTo(x, y);\n                            } else {\n                                ctx.lineTo(x, y);\n                            }\n                        }\n                        ctx.stroke();\n                    }\n                    // رسم شبكة سداسية متحركة\n                    const hexSize = 60;\n                    const hexRows = Math.ceil(canvas.height / (hexSize * 0.75)) + 2;\n                    const hexCols = Math.ceil(canvas.width / (hexSize * Math.sqrt(3))) + 2;\n                    ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(Math.max(0.01, 0.04 + Math.sin(time * 0.5) * 0.02), \")\");\n                    ctx.lineWidth = 1;\n                    for(let row = 0; row < hexRows; row++){\n                        for(let col = 0; col < hexCols; col++){\n                            const x = col * hexSize * Math.sqrt(3) + row % 2 * hexSize * Math.sqrt(3) * 0.5;\n                            const y = row * hexSize * 0.75;\n                            const distanceToMouse = Math.sqrt((x - mouseX) ** 2 + (y - mouseY) ** 2);\n                            const influence = Math.max(0, 1 - distanceToMouse / 300); // زيادة نطاق التأثير\n                            if (influence > 0.05) {\n                                // تحسين الحركة والحجم حسب قرب الماوس\n                                const moveIntensity = influence * 15;\n                                const sizeMultiplier = 0.2 + influence * 0.8;\n                                const alpha = 0.02 + influence * 0.1;\n                                ctx.strokeStyle = \"rgba(139, 92, 246, \".concat(alpha, \")\");\n                                ctx.lineWidth = 1 + influence * 2;\n                                drawHexagon(ctx, x + Math.sin(time + row + col) * moveIntensity, y + Math.cos(time + row + col) * moveIntensity, hexSize * sizeMultiplier);\n                            }\n                        }\n                    }\n                    // رسم دوائر متوهجة ناعمة\n                    const glowCircles = 5;\n                    for(let i = 0; i < glowCircles; i++){\n                        const x = canvas.width * (0.1 + i * 0.2);\n                        const y = canvas.height * (0.2 + Math.sin(time * 0.3 + i) * 0.3);\n                        const radius = Math.max(10, 150 + Math.sin(time * 0.4 + i) * 50);\n                        const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius);\n                        const colors = [\n                            \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.05 + Math.sin(time + i) * 0.02), \")\"),\n                            \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.03 + Math.sin(time + i + 1) * 0.015), \")\"),\n                            \"rgba(251, 191, 36, \".concat(Math.max(0.01, 0.02 + Math.sin(time + i + 2) * 0.01), \")\")\n                        ];\n                        gradient.addColorStop(0, colors[i % colors.length]);\n                        gradient.addColorStop(0.7, colors[(i + 1) % colors.length].replace(/[\\d.]+\\)/, '0.01)'));\n                        gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        ctx.arc(x, y, radius, 0, Math.PI * 2);\n                        ctx.fill();\n                    }\n                    // رسم أشكال هندسية متحركة\n                    const shapes = 3;\n                    for(let i = 0; i < shapes; i++){\n                        const x = canvas.width * (0.3 + i * 0.2);\n                        const y = canvas.height * (0.4 + Math.cos(time * 0.2 + i) * 0.2);\n                        const size = Math.max(10, 30 + Math.sin(time + i) * 10);\n                        const rotation = time * 0.5 + i;\n                        ctx.save();\n                        ctx.translate(x, y);\n                        ctx.rotate(rotation);\n                        const gradient = ctx.createLinearGradient(-size, -size, size, size);\n                        gradient.addColorStop(0, \"rgba(168, 85, 247, \".concat(Math.max(0.01, 0.1 + Math.sin(time + i) * 0.05), \")\"));\n                        gradient.addColorStop(1, \"rgba(236, 72, 153, \".concat(Math.max(0.01, 0.05 + Math.cos(time + i) * 0.03), \")\"));\n                        ctx.fillStyle = gradient;\n                        ctx.beginPath();\n                        // استخدام rect بدلاً من roundRect للتوافق مع جميع المتصفحات\n                        ctx.rect(-size / 2, -size / 2, size, size);\n                        ctx.fill();\n                        ctx.restore();\n                    }\n                    // تحديث ورسم الجسيمات الذكية المتطورة\n                    particlesRef.current.forEach({\n                        \"SimpleHeroSection.useEffect.animate\": (particle, index)=>{\n                            // التأكد من صحة القيم\n                            particle.size = Math.max(1, particle.size || 2);\n                            particle.energy = Math.max(50, Math.min(100, particle.energy || 50));\n                            particle.opacity = Math.max(0.1, Math.min(1, particle.opacity || 0.5));\n                            // حفظ الموضع السابق للمسار\n                            particle.trail.push({\n                                x: particle.x,\n                                y: particle.y\n                            });\n                            if (particle.trail.length > 5) {\n                                particle.trail.shift();\n                            }\n                            // تحديث الموضع مع فيزياء متقدمة\n                            particle.x += particle.vx;\n                            particle.y += particle.vy;\n                            particle.pulse += particle.pulseSpeed;\n                            // تأثير الماوس المغناطيسي المتطور والأكثر ذكاءً\n                            const mouseInfluence = 250; // نطاق تأثير أوسع\n                            const dx = mouseX - particle.x;\n                            const dy = mouseY - particle.y;\n                            const distance = Math.sqrt(dx * dx + dy * dy);\n                            if (distance < mouseInfluence) {\n                                const force = (mouseInfluence - distance) / mouseInfluence * particle.magnetism;\n                                const angle = Math.atan2(dy, dx);\n                                // قوة جذب متدرجة حسب المسافة\n                                const attractionForce = force * 0.012;\n                                particle.vx += Math.cos(angle) * attractionForce;\n                                particle.vy += Math.sin(angle) * attractionForce;\n                                // تأثير الطاقة المحسن مع تسارع\n                                particle.energy = Math.min(100, particle.energy + force * 6);\n                                // حركة موجية إضافية\n                                particle.phase += 0.1;\n                                const waveForce = Math.sin(particle.phase) * force * 0.003;\n                                particle.vx += Math.cos(angle + Math.PI / 2) * waveForce;\n                                particle.vy += Math.sin(angle + Math.PI / 2) * waveForce;\n                                // تأثير اهتزاز ذكي\n                                const vibrationIntensity = force * 0.004;\n                                particle.vx += (Math.random() - 0.5) * vibrationIntensity;\n                                particle.vy += (Math.random() - 0.5) * vibrationIntensity;\n                                // تسريع الدوران عند القرب من الماوس\n                                particle.rotationSpeed += force * 0.001;\n                            } else {\n                                // تقليل الطاقة والدوران تدريجياً\n                                particle.energy = Math.max(50, particle.energy - 0.2);\n                                particle.rotationSpeed *= 0.98;\n                            }\n                            // تطبيق الاحتكاك\n                            particle.vx *= 0.99;\n                            particle.vy *= 0.99;\n                            // حدود الشاشة مع ارتداد ديناميكي\n                            if (particle.x < 0 || particle.x > canvas.width) {\n                                particle.vx *= -0.7;\n                                particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n                            }\n                            if (particle.y < 0 || particle.y > canvas.height) {\n                                particle.vy *= -0.7;\n                                particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n                            }\n                            // رسم مسار الجسيمة\n                            if (particle.trail.length > 1) {\n                                ctx.beginPath();\n                                ctx.strokeStyle = particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba');\n                                ctx.lineWidth = 1;\n                                for(let i = 0; i < particle.trail.length - 1; i++){\n                                    const alpha = i / particle.trail.length;\n                                    ctx.globalAlpha = alpha * 0.3;\n                                    if (i === 0) {\n                                        ctx.moveTo(particle.trail[i].x, particle.trail[i].y);\n                                    } else {\n                                        ctx.lineTo(particle.trail[i].x, particle.trail[i].y);\n                                    }\n                                }\n                                ctx.stroke();\n                            }\n                            // رسم الجسيمة مع تأثيرات متقدمة\n                            const energyFactor = particle.energy / 100;\n                            const pulseSize = Math.max(1, particle.size + Math.sin(particle.pulse) * 2 * energyFactor);\n                            const pulseOpacity = Math.max(0.1, particle.opacity + Math.sin(particle.pulse) * 0.3 * energyFactor);\n                            // رسم الهالة الخارجية\n                            const outerRadius = Math.max(1, pulseSize * 4);\n                            const outerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, outerRadius);\n                            outerGradient.addColorStop(0, particle.color.replace(')', ', 0.4)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(0.5, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            outerGradient.addColorStop(1, particle.color.replace(')', ', 0)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = outerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.6 * energyFactor;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, outerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الهالة الداخلية\n                            const innerRadius = Math.max(1, pulseSize * 2);\n                            const innerGradient = ctx.createRadialGradient(particle.x, particle.y, 0, particle.x, particle.y, innerRadius);\n                            innerGradient.addColorStop(0, particle.color.replace(')', ', 0.8)').replace('rgb', 'rgba'));\n                            innerGradient.addColorStop(1, particle.color.replace(')', ', 0.2)').replace('rgb', 'rgba'));\n                            ctx.fillStyle = innerGradient;\n                            ctx.globalAlpha = pulseOpacity * 0.8;\n                            ctx.beginPath();\n                            ctx.arc(particle.x, particle.y, innerRadius, 0, Math.PI * 2);\n                            ctx.fill();\n                            // رسم الجسيمة الأساسية بأشكال مختلفة\n                            ctx.save();\n                            ctx.translate(particle.x, particle.y);\n                            ctx.rotate(particle.pulse * particle.rotationSpeed);\n                            ctx.fillStyle = particle.color;\n                            ctx.globalAlpha = Math.max(0.1, Math.min(1, pulseOpacity));\n                            const finalSize = Math.max(0.5, pulseSize);\n                            // رسم حسب نوع الجسيمة\n                            if (particle.type === 0) {\n                                // دائرة\n                                ctx.beginPath();\n                                ctx.arc(0, 0, finalSize, 0, Math.PI * 2);\n                                ctx.fill();\n                            } else if (particle.type === 1) {\n                                // مربع\n                                ctx.beginPath();\n                                ctx.rect(-finalSize, -finalSize, finalSize * 2, finalSize * 2);\n                                ctx.fill();\n                            } else {\n                                // نجمة\n                                ctx.beginPath();\n                                const spikes = 5;\n                                const outerRadius = finalSize;\n                                const innerRadius = finalSize * 0.5;\n                                for(let i = 0; i < spikes * 2; i++){\n                                    const radius = i % 2 === 0 ? outerRadius : innerRadius;\n                                    const angle = i * Math.PI / spikes;\n                                    const x = Math.cos(angle) * radius;\n                                    const y = Math.sin(angle) * radius;\n                                    if (i === 0) {\n                                        ctx.moveTo(x, y);\n                                    } else {\n                                        ctx.lineTo(x, y);\n                                    }\n                                }\n                                ctx.closePath();\n                                ctx.fill();\n                            }\n                            ctx.restore();\n                            // رسم خطوط الاتصال الذكية المتطورة\n                            particlesRef.current.forEach({\n                                \"SimpleHeroSection.useEffect.animate\": (otherParticle, otherIndex)=>{\n                                    if (index !== otherIndex && index < otherIndex) {\n                                        const dx = particle.x - otherParticle.x;\n                                        const dy = particle.y - otherParticle.y;\n                                        const distance = Math.sqrt(dx * dx + dy * dy);\n                                        if (distance < 150) {\n                                            const opacity = Math.max(0, 0.2 * (1 - distance / 150));\n                                            const energyBonus = (particle.energy + otherParticle.energy) / 200;\n                                            const finalOpacity = Math.max(0.01, Math.min(1, opacity * (1 + energyBonus)));\n                                            // خط متدرج ديناميكي\n                                            const gradient = ctx.createLinearGradient(particle.x, particle.y, otherParticle.x, otherParticle.y);\n                                            const color1 = particle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const color2 = otherParticle.color.replace(')', ', ' + finalOpacity + ')').replace('rgb', 'rgba');\n                                            const midColor = \"rgba(192, 132, 252, \".concat(finalOpacity * 1.2, \")\");\n                                            gradient.addColorStop(0, color1);\n                                            gradient.addColorStop(0.5, midColor);\n                                            gradient.addColorStop(1, color2);\n                                            // رسم خط متموج\n                                            ctx.beginPath();\n                                            ctx.strokeStyle = gradient;\n                                            ctx.lineWidth = 1 + finalOpacity * 3;\n                                            const steps = 10;\n                                            for(let i = 0; i <= steps; i++){\n                                                const t = i / steps;\n                                                const x = particle.x + (otherParticle.x - particle.x) * t;\n                                                const y = particle.y + (otherParticle.y - particle.y) * t;\n                                                // إضافة تموج ناعم\n                                                const waveOffset = Math.sin(t * Math.PI * 2 + time) * 5 * finalOpacity;\n                                                const perpX = -(otherParticle.y - particle.y) / distance;\n                                                const perpY = (otherParticle.x - particle.x) / distance;\n                                                const finalX = x + perpX * waveOffset;\n                                                const finalY = y + perpY * waveOffset;\n                                                if (i === 0) {\n                                                    ctx.moveTo(finalX, finalY);\n                                                } else {\n                                                    ctx.lineTo(finalX, finalY);\n                                                }\n                                            }\n                                            ctx.stroke();\n                                            // إضافة نقاط ضوئية على الخط\n                                            if (finalOpacity > 0.1) {\n                                                const midX = (particle.x + otherParticle.x) / 2;\n                                                const midY = (particle.y + otherParticle.y) / 2;\n                                                ctx.beginPath();\n                                                ctx.arc(midX, midY, Math.max(0.5, 2 * finalOpacity), 0, Math.PI * 2);\n                                                ctx.fillStyle = \"rgba(255, 255, 255, \".concat(Math.max(0.1, finalOpacity * 0.8), \")\");\n                                                ctx.fill();\n                                            }\n                                        }\n                                    }\n                                }\n                            }[\"SimpleHeroSection.useEffect.animate\"]);\n                        }\n                    }[\"SimpleHeroSection.useEffect.animate\"]);\n                    ctx.globalAlpha = 1;\n                    animationRef.current = requestAnimationFrame(animate);\n                }\n            }[\"SimpleHeroSection.useEffect.animate\"];\n            animate();\n            // التنظيف\n            return ({\n                \"SimpleHeroSection.useEffect\": ()=>{\n                    if (animationRef.current) {\n                        cancelAnimationFrame(animationRef.current);\n                    }\n                    window.removeEventListener('resize', resizeCanvas);\n                    window.removeEventListener('mousemove', handleMouseMove);\n                }\n            })[\"SimpleHeroSection.useEffect\"];\n        }\n    }[\"SimpleHeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-white via-purple-50 to-pink-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"absolute inset-0 w-full h-full\",\n                style: {\n                    zIndex: 1\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 568,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-white/30 via-transparent to-purple-50/20\",\n                style: {\n                    zIndex: 2\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 575,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 text-center px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"bg-gradient-to-r from-purple-600 via-pink-600 to-purple-600 bg-clip-text text-transparent\",\n                                    children: \"TechnoFlash\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 580,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl md:text-2xl text-gray-700 mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"منصتك الشاملة لأحدث التقنيات وأدوات الذكاء الاصطناعي\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 579,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/articles\",\n                                className: \"group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"استكشف المقالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:translate-x-1 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 7l5 5m0 0l-5 5m5-5H6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/ai-tools\",\n                                className: \"group border-2 border-purple-400 text-purple-400 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-purple-400 hover:text-white transition-all duration-300 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أدوات الذكاء الاصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 group-hover:rotate-12 transition-transform\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 591,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"100+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 616,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"مقال تقني\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 617,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 615,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"50+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 620,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"أداة ذكاء اصطناعي\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl md:text-4xl font-bold text-gray-900 mb-2\",\n                                        children: \"1000+\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 624,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"قارئ نشط\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 614,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-bounce\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-2 border-gray-400/60 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-4 h-4 text-gray-600/80\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                                lineNumber: 635,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                            lineNumber: 634,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                        lineNumber: 633,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                    lineNumber: 632,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\New folder (4)\\\\techno-flashi\\\\src\\\\components\\\\SimpleHeroSection.tsx\",\n        lineNumber: 566,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleHeroSection, \"/8IKySTmGZxFymijsBTHUGtuaQs=\");\n_c = SimpleHeroSection;\nvar _c;\n$RefreshReg$(_c, \"SimpleHeroSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SimpleHeroSection.tsx\n"));

/***/ })

});